# -*- coding: utf-8 -*-
"""
شاشة طلب حوالة - نظام إدارة الحوالات
Remittance Request Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QFormLayout, QLabel, QPushButton, QLineEdit,
                               QComboBox, QTextEdit, QDateEdit,
                               QFrame, QGroupBox, QGridLayout, QMessageBox,
                               QProgressBar, QCheckBox, QSpinBox, QTabWidget,
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QSplitter, QScrollArea, QToolBar, QStatusBar,
                               QCompleter)
from PySide6.QtCore import Qt, Signal, QDate, QTimer, QThread, QStringListModel
from PySide6.QtGui import QFont, QPixmap, QIcon, QAction

import sqlite3
from pathlib import Path
from datetime import datetime
import json
import uuid

class RemittanceRequestWindow(QMainWindow):
    """شاشة طلب حوالة الرئيسية"""
    
    # إشارات للتواصل مع النوافذ الأخرى
    remittance_request_created = Signal(dict)  # إشارة عند إنشاء طلب الحوالة
    send_to_create_remittance = Signal(dict)  # إشارة لإرسال البيانات لنافذة إنشاء الحوالة
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("طلب حوالة - نظام إدارة الحوالات المتقدم")
        self.setMinimumSize(1200, 900)

        # فتح النافذة في وضع ملء الشاشة
        self.showMaximized()

        # متغيرات النافذة
        self.current_requests = []
        self.selected_request_id = None
        self.editing_request_id = None  # معرف الطلب قيد التحرير

        # متغيرات نظام الحالات المتقدم
        self.window_mode = "DISABLED"  # DISABLED, NEW, EDIT
        self.current_editing_request_id = None
        self.form_fields = []  # قائمة جميع حقول النموذج للتحكم في التفعيل
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        
        # تحميل البيانات الأولية
        self.load_requests()
        self.load_filters_data()
        
        # تطبيق الستايل العصري
        self.apply_modern_style()

        # تعطيل النافذة افتراضياً (التحسين العملاق)
        self.set_window_mode("DISABLED")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # العنوان الرئيسي
        header_frame = self.create_header()
        layout.addWidget(header_frame)

        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #3498db;
                border-radius: 10px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 12px 25px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)

        # تبويب قائمة الطلبات
        requests_list_tab = self.create_requests_list_tab()
        self.tab_widget.addTab(requests_list_tab, "📋 قائمة طلبات الحوالات")

        # تبويب إنشاء طلب جديد
        new_request_tab = self.create_new_request_tab()
        self.tab_widget.addTab(new_request_tab, "➕ طلب حوالة جديد")

        # تبويب التقارير والإحصائيات
        reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(reports_tab, "📊 التقارير والإحصائيات")

        # تبويب دفتر العناوين
        address_book_tab = self.create_address_book_tab()
        self.tab_widget.addTab(address_book_tab, "📇 دفتر العناوين")

        layout.addWidget(self.tab_widget)

    def create_header(self):
        """إنشاء رأس النافذة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(25, 20, 25, 20)

        # أيقونة
        icon_label = QLabel("📝")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 56px;
                color: white;
                background: transparent;
            }
        """)
        layout.addWidget(icon_label)

        # النص
        text_layout = QVBoxLayout()
        
        title_label = QLabel("طلب حوالة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                font-weight: bold;
                color: white;
                background: transparent;
            }
        """)
        text_layout.addWidget(title_label)

        subtitle_label = QLabel("نظام إدارة طلبات الحوالات المتقدم")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #ecf0f1;
                background: transparent;
            }
        """)
        text_layout.addWidget(subtitle_label)

        layout.addLayout(text_layout)
        layout.addStretch()

        # إحصائيات سريعة
        stats_layout = QVBoxLayout()
        
        # عدد الطلبات اليوم
        today_requests_label = QLabel("طلبات اليوم: 0")
        today_requests_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: white;
                background: rgba(255,255,255,0.2);
                padding: 8px 15px;
                border-radius: 8px;
                font-weight: bold;
            }
        """)
        stats_layout.addWidget(today_requests_label)

        # الطلبات المعلقة
        pending_requests_label = QLabel("طلبات معلقة: 0")
        pending_requests_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: white;
                background: rgba(255,255,255,0.2);
                padding: 8px 15px;
                border-radius: 8px;
                font-weight: bold;
            }
        """)
        stats_layout.addWidget(pending_requests_label)

        layout.addLayout(stats_layout)

        return frame

    def create_requests_list_tab(self):
        """إنشاء تبويب قائمة الطلبات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # قسم المرشحات
        filters_frame = self.create_filters_section()
        layout.addWidget(filters_frame)

        # جدول الطلبات
        self.requests_table = QTableWidget()
        self.requests_table.setColumnCount(10)
        self.requests_table.setHorizontalHeaderLabels([
            "رقم الطلب", "اسم المرسل", "اسم المستقبل", "المبلغ", 
            "العملة", "البلد المستقبل", "تاريخ الطلب", "الحالة", 
            "الأولوية", "الإجراءات"
        ])
        
        # تنسيق الجدول
        self.requests_table.horizontalHeader().setStretchLastSection(True)
        self.requests_table.setAlternatingRowColors(True)
        self.requests_table.setSelectionBehavior(QTableWidget.SelectRows)

        # زيادة ارتفاع الصفوف
        self.requests_table.verticalHeader().setDefaultSectionSize(50)

        self.requests_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                gridline-color: #ecf0f1;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 40px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                min-height: 35px;
            }
        """)
        
        layout.addWidget(self.requests_table)

        # أزرار العمليات
        buttons_frame = self.create_list_buttons_section()
        layout.addWidget(buttons_frame)

        return widget

    def create_filters_section(self):
        """إنشاء قسم المرشحات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        layout = QGridLayout(frame)
        layout.setSpacing(15)

        # مرشح الحالة
        layout.addWidget(QLabel("🔍 الحالة:"), 0, 0)
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "معلق", "مؤكد", "مرفوض", "مكتمل"])
        layout.addWidget(self.status_filter, 0, 1)

        # مرشح التاريخ من
        layout.addWidget(QLabel("📅 من تاريخ:"), 0, 2)
        self.date_from_filter = QDateEdit()
        self.date_from_filter.setDate(QDate.currentDate().addDays(-30))
        self.date_from_filter.setCalendarPopup(True)
        layout.addWidget(self.date_from_filter, 0, 3)

        # مرشح التاريخ إلى
        layout.addWidget(QLabel("📅 إلى تاريخ:"), 0, 4)
        self.date_to_filter = QDateEdit()
        self.date_to_filter.setDate(QDate.currentDate())
        self.date_to_filter.setCalendarPopup(True)
        layout.addWidget(self.date_to_filter, 0, 5)

        # زر البحث
        search_btn = QPushButton("🔍 بحث")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        search_btn.clicked.connect(self.filter_requests)
        layout.addWidget(search_btn, 0, 6)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        refresh_btn.clicked.connect(self.load_requests)
        layout.addWidget(refresh_btn, 0, 7)

        return frame

    def create_list_buttons_section(self):
        """إنشاء قسم أزرار قائمة الطلبات"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.addStretch()

        # زر عرض التفاصيل
        view_details_btn = QPushButton("👁️ عرض التفاصيل")
        view_details_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        view_details_btn.clicked.connect(self.view_request_details)
        layout.addWidget(view_details_btn)

        # زر إرسال لإنشاء حوالة
        send_to_create_btn = QPushButton("💸 إرسال لإنشاء حوالة")
        send_to_create_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 180px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        send_to_create_btn.clicked.connect(self.send_selected_to_create_remittance)
        layout.addWidget(send_to_create_btn)

        # زر تحديث الحالة
        update_status_btn = QPushButton("📝 تحديث الحالة")
        update_status_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        update_status_btn.clicked.connect(self.update_request_status)
        layout.addWidget(update_status_btn)

        # زر حذف
        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(self.delete_request)
        layout.addWidget(delete_btn)

        return frame

    def create_new_request_tab(self):
        """إنشاء تبويب طلب جديد"""
        widget = QWidget()

        # إنشاء scroll area للتمرير
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(widget)

        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # البيانات الأساسية (قسم جديد)
        basic_data_group = self.create_basic_data_section()
        layout.addWidget(basic_data_group)

        # معلومات المرسل
        sender_group = self.create_sender_info_section()
        layout.addWidget(sender_group)

        # معلومات المستقبل
        receiver_group = self.create_receiver_info_section()
        layout.addWidget(receiver_group)

        # الملاحظات والخيارات
        notes_group = self.create_notes_options_section()
        layout.addWidget(notes_group)

        # أزرار الإجراءات
        actions_frame = self.create_new_request_buttons()
        layout.addWidget(actions_frame)

        layout.addStretch()

        # إنشاء container widget
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.addWidget(scroll)

        # جمع جميع حقول النموذج للتحكم في التفعيل
        self.collect_form_fields()

        return container

    def create_basic_data_section(self):
        """إنشاء قسم البيانات الأساسية"""
        group = QGroupBox("📋 البيانات الأساسية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #27ae60;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # التاريخ
        layout.addWidget(QLabel("📅 التاريخ:"), 0, 0)
        self.request_date_input = QDateEdit()
        self.request_date_input.setDate(QDate.currentDate())
        self.request_date_input.setCalendarPopup(True)
        layout.addWidget(self.request_date_input, 0, 1)

        # رقم الطلب (تلقائي)
        layout.addWidget(QLabel("🔢 رقم الطلب:"), 0, 2)
        self.request_number_input = QLineEdit()
        self.request_number_input.setReadOnly(True)
        self.request_number_input.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                color: #6c757d;
                font-weight: bold;
            }
        """)
        # إنشاء رقم طلب تلقائي
        auto_number = f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}"
        self.request_number_input.setText(auto_number)
        layout.addWidget(self.request_number_input, 0, 3)

        # الفرع (قائمة منسدلة)
        layout.addWidget(QLabel("🏢 الفرع:"), 1, 0)
        self.branch_combo = QComboBox()
        self.branch_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        layout.addWidget(self.branch_combo, 1, 1)

        # اسم الصراف (قائمة منسدلة)
        layout.addWidget(QLabel("👤 اسم الصراف:"), 1, 2)
        self.exchanger_combo = QComboBox()
        self.exchanger_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                min-width: 200px;
            }
            QComboBox:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        layout.addWidget(self.exchanger_combo, 1, 3)

        # العملة (قائمة منسدلة)
        layout.addWidget(QLabel("💱 العملة:"), 2, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        layout.addWidget(self.currency_combo, 2, 1)

        # مبلغ الحوالة
        layout.addWidget(QLabel("💰 مبلغ الحوالة:"), 2, 2)
        self.remittance_amount_input = QLineEdit()
        self.remittance_amount_input.setPlaceholderText("أدخل مبلغ الحوالة...")
        self.remittance_amount_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                background-color: #f9fff9;
            }
        """)
        layout.addWidget(self.remittance_amount_input, 2, 3)

        # الغرض من التحويل
        layout.addWidget(QLabel("📝 الغرض من التحويل:"), 3, 0)
        self.transfer_purpose_input = QLineEdit()
        self.transfer_purpose_input.setPlaceholderText("أدخل الغرض من التحويل...")
        self.transfer_purpose_input.setText("COST OF FOODSTUFF")  # القيمة الافتراضية
        layout.addWidget(self.transfer_purpose_input, 3, 1, 1, 3)

        return group

    def create_sender_info_section(self):
        """إنشاء قسم معلومات المرسل"""
        group = QGroupBox("👤 معلومات المرسل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # اسم المرسل
        layout.addWidget(QLabel("👤 الاسم الكامل:"), 0, 0)
        self.sender_name_input = QLineEdit()
        self.sender_name_input.setPlaceholderText("أدخل الاسم الكامل للمرسل...")
        # تعبئة البيانات الافتراضية
        self.sender_name_input.setText("ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD")
        layout.addWidget(self.sender_name_input, 0, 1)

        # الجهة (بدلاً من رقم الهوية)
        layout.addWidget(QLabel("🏛️ الجهة:"), 0, 2)
        self.sender_entity_input = QLineEdit()
        self.sender_entity_input.setPlaceholderText("أدخل اسم الجهة...")
        # تعبئة البيانات الافتراضية
        self.sender_entity_input.setText("G.M: NASHA'AT RASHAD QASIM ALDUBAEE")
        layout.addWidget(self.sender_entity_input, 0, 3)

        # رقم الهاتف
        layout.addWidget(QLabel("📱 رقم الهاتف:"), 1, 0)
        self.sender_phone_input = QLineEdit()
        self.sender_phone_input.setPlaceholderText("أدخل رقم الهاتف...")
        # تعبئة البيانات الافتراضية
        self.sender_phone_input.setText("+967 1 616109")
        layout.addWidget(self.sender_phone_input, 1, 1)

        # رقم الفاكس
        layout.addWidget(QLabel("📠 رقم الفاكس:"), 1, 2)
        self.sender_fax_input = QLineEdit()
        self.sender_fax_input.setPlaceholderText("أدخل رقم الفاكس...")
        # تعبئة البيانات الافتراضية
        self.sender_fax_input.setText("+967 1 615909")
        layout.addWidget(self.sender_fax_input, 1, 3)

        # رقم الموبايل (نُقل إلى الصف الثاني)
        layout.addWidget(QLabel("📲 رقم الموبايل:"), 2, 0)
        self.sender_mobile_input = QLineEdit()
        self.sender_mobile_input.setPlaceholderText("أدخل رقم الموبايل...")
        # تعبئة البيانات الافتراضية
        self.sender_mobile_input.setText("+967 *********")
        layout.addWidget(self.sender_mobile_input, 2, 1)

        # ص.ب (صندوق بريد) (نُقل إلى الصف الثاني)
        layout.addWidget(QLabel("📮 ص.ب:"), 2, 2)
        self.sender_pobox_input = QLineEdit()
        self.sender_pobox_input.setPlaceholderText("أدخل صندوق البريد...")
        # تعبئة البيانات الافتراضية
        self.sender_pobox_input.setText("1903")
        layout.addWidget(self.sender_pobox_input, 2, 3)

        # البريد الإلكتروني
        layout.addWidget(QLabel("📧 البريد الإلكتروني:"), 3, 0)
        self.sender_email_input = QLineEdit()
        self.sender_email_input.setPlaceholderText("أدخل البريد الإلكتروني...")
        # تعبئة البيانات الافتراضية
        self.sender_email_input.setText("<EMAIL>, <EMAIL>")
        layout.addWidget(self.sender_email_input, 3, 1)

        # العنوان
        layout.addWidget(QLabel("📍 العنوان:"), 3, 2)
        self.sender_address_input = QLineEdit()
        self.sender_address_input.setPlaceholderText("أدخل العنوان...")
        # تعبئة البيانات الافتراضية
        self.sender_address_input.setText("TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN")
        layout.addWidget(self.sender_address_input, 3, 3)

        return group

    def create_receiver_info_section(self):
        """إنشاء قسم معلومات المستقبل"""
        group = QGroupBox("👥 معلومات المستقبل")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #e74c3c;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # اسم المستقبل مع البحث التلقائي
        layout.addWidget(QLabel("👤 الاسم الكامل:"), 0, 0)
        self.receiver_name_input = QLineEdit()
        self.receiver_name_input.setPlaceholderText("أدخل الاسم الكامل للمستقبل...")

        # إعداد البحث التلقائي
        self.setup_receiver_name_autocomplete()

        layout.addWidget(self.receiver_name_input, 0, 1)

        # رقم الحساب (بدلاً من رقم الهوية)
        layout.addWidget(QLabel("🏦 رقم الحساب:"), 0, 2)
        self.receiver_account_input = QLineEdit()
        self.receiver_account_input.setPlaceholderText("أدخل رقم الحساب...")
        layout.addWidget(self.receiver_account_input, 0, 3)

        # اسم البنك (بدلاً من رقم الهاتف)
        layout.addWidget(QLabel("🏛️ اسم البنك:"), 1, 0)
        self.receiver_bank_input = QLineEdit()
        self.receiver_bank_input.setPlaceholderText("أدخل اسم البنك...")
        layout.addWidget(self.receiver_bank_input, 1, 1)

        # فرع البنك
        layout.addWidget(QLabel("🏢 فرع البنك:"), 1, 2)
        self.receiver_bank_branch_input = QLineEdit()
        self.receiver_bank_branch_input.setPlaceholderText("أدخل فرع البنك...")
        layout.addWidget(self.receiver_bank_branch_input, 1, 3)

        # السويفت
        layout.addWidget(QLabel("🔗 السويفت:"), 2, 0)
        self.receiver_swift_input = QLineEdit()
        self.receiver_swift_input.setPlaceholderText("أدخل رمز السويفت...")
        layout.addWidget(self.receiver_swift_input, 2, 1)

        # البلد (حقل نصي)
        layout.addWidget(QLabel("🌍 البلد:"), 2, 2)
        self.receiver_country_input = QLineEdit()
        self.receiver_country_input.setPlaceholderText("أدخل البلد...")
        layout.addWidget(self.receiver_country_input, 2, 3)

        # بلد البنك
        layout.addWidget(QLabel("🏦 بلد البنك:"), 3, 0)
        self.receiver_bank_country_input = QLineEdit()
        self.receiver_bank_country_input.setPlaceholderText("أدخل بلد البنك...")
        layout.addWidget(self.receiver_bank_country_input, 3, 1)

        # العنوان
        layout.addWidget(QLabel("📍 العنوان:"), 3, 2)
        self.receiver_address_input = QLineEdit()
        self.receiver_address_input.setPlaceholderText("أدخل العنوان...")
        layout.addWidget(self.receiver_address_input, 3, 3)

        # زر دفتر العناوين
        address_book_btn = QPushButton("📇 دفتر العناوين")
        address_book_btn.setStyleSheet("""
            QPushButton {
                background-color: #8e44ad;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #9b59b6;
            }
            QPushButton:pressed {
                background-color: #7d3c98;
            }
        """)
        address_book_btn.clicked.connect(self.open_address_book_tab)
        layout.addWidget(address_book_btn, 4, 0, 1, 2)

        return group



    def create_notes_options_section(self):
        """إنشاء قسم الملاحظات والخيارات"""
        group = QGroupBox("📝 ملاحظات وخيارات إضافية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #2c3e50;
                border: 3px solid #9b59b6;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                border-radius: 5px;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 25, 20, 20)

        # الملاحظات
        notes_label = QLabel("📝 ملاحظات إضافية:")
        layout.addWidget(notes_label)

        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل أي ملاحظات إضافية حول طلب الحوالة...")
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                background-color: #fafafa;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: white;
            }
        """)
        layout.addWidget(self.notes_input)

        # الخيارات
        options_layout = QGridLayout()

        # إشعار SMS
        self.sms_notification_check = QCheckBox("📱 إرسال إشعار SMS للمستقبل")
        self.sms_notification_check.setChecked(True)
        options_layout.addWidget(self.sms_notification_check, 0, 0)

        # إشعار بريد إلكتروني
        self.email_notification_check = QCheckBox("📧 إرسال إشعار بريد إلكتروني")
        options_layout.addWidget(self.email_notification_check, 0, 1)

        # إنشاء حوالة تلقائي
        self.auto_create_remittance_check = QCheckBox("🚀 إنشاء حوالة تلقائياً عند الموافقة")
        self.auto_create_remittance_check.setChecked(True)
        options_layout.addWidget(self.auto_create_remittance_check, 1, 0, 1, 2)

        layout.addLayout(options_layout)

        return group

    def create_new_request_buttons(self):
        """إنشاء أزرار طلب جديد مع نظام الحالات المتقدم"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(15)

        # مؤشر حالة النموذج
        self.form_status_label = QLabel("🔒 النموذج معطل - اختر عملية")
        self.form_status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #e74c3c;
                padding: 10px 20px;
                background-color: #fadbd8;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin-right: 20px;
            }
        """)
        layout.addWidget(self.form_status_label)

        layout.addStretch()

        # زر إضافة طلب جديد
        self.add_new_btn = QPushButton("➕ إضافة طلب جديد")
        self.add_new_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 160px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.add_new_btn.clicked.connect(self.enable_new_mode)
        layout.addWidget(self.add_new_btn)

        # زر تعديل طلب
        self.edit_btn = QPushButton("✏️ تعديل طلب")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 140px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d35400);
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.edit_btn.clicked.connect(self.enable_edit_mode)
        layout.addWidget(self.edit_btn)

        # زر حفظ الطلب
        self.save_request_btn = QPushButton("💾 حفظ طلب الحوالة")
        self.save_request_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 180px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.save_request_btn.clicked.connect(self.save_new_request)
        layout.addWidget(self.save_request_btn)

        # زر إلغاء العملية
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.cancel_btn.clicked.connect(self.cancel_operation)
        layout.addWidget(self.cancel_btn)



        # زر طباعة PDF
        print_pdf_btn = QPushButton("🖨️ طباعة PDF")
        print_pdf_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #9b59b6, stop:1 #8e44ad);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 200px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8e44ad, stop:1 #7d3c98);
            }
        """)
        print_pdf_btn.clicked.connect(self.generate_pdf_report)
        layout.addWidget(print_pdf_btn)

        # زر إرسال لإنشاء حوالة
        send_to_create_btn = QPushButton("💸 إرسال لإنشاء حوالة")
        send_to_create_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 200px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        send_to_create_btn.clicked.connect(self.send_new_request_to_create_remittance)
        layout.addWidget(send_to_create_btn)

        # زر إلغاء التحرير
        self.cancel_edit_btn = QPushButton("❌ إلغاء التحرير")
        self.cancel_edit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 180px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d68910);
            }
        """)
        self.cancel_edit_btn.clicked.connect(self.cancel_editing)
        self.cancel_edit_btn.setVisible(False)  # مخفي افتراضياً
        layout.addWidget(self.cancel_edit_btn)

        # زر مسح النموذج
        clear_form_btn = QPushButton("🗑️ مسح النموذج")
        clear_form_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
        """)
        clear_form_btn.clicked.connect(self.clear_form)
        layout.addWidget(clear_form_btn)

        return frame

    def create_reports_tab(self):
        """إنشاء تبويب التقارير والإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # إحصائيات سريعة
        stats_frame = self.create_quick_stats()
        layout.addWidget(stats_frame)

        # رسم بياني للطلبات
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        chart_layout = QVBoxLayout(chart_frame)

        chart_title = QLabel("📊 إحصائيات طلبات الحوالات")
        chart_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        chart_layout.addWidget(chart_title)

        # هنا يمكن إضافة رسم بياني لاحقاً
        chart_placeholder = QLabel("📈 الرسم البياني قيد التطوير...")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                padding: 50px;
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
            }
        """)
        chart_layout.addWidget(chart_placeholder)

        layout.addWidget(chart_frame)
        layout.addStretch()

        return widget

    def create_address_book_tab(self):
        """إنشاء تبويب دفتر العناوين مبسط وشامل"""
        # إنشاء الحاوي الرئيسي
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # عنوان التبويب
        title_label = QLabel("📇 إدارة دفتر العناوين")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # شريط البحث والأزرار العلوية
        top_controls_frame = self.create_address_book_top_controls()
        main_layout.addWidget(top_controls_frame)

        # نموذج إضافة/تعديل عنوان
        form_frame = self.create_address_book_form()
        main_layout.addWidget(form_frame)

        # جدول دفتر العناوين
        table_frame = self.create_address_book_table()
        main_layout.addWidget(table_frame)

        # إنشاء scroll area للتمرير
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(main_widget)
        scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #f8f9fa;
            }
        """)

        return scroll

    def create_address_book_top_controls(self):
        """إنشاء شريط التحكم العلوي لدفتر العناوين"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)

        layout = QVBoxLayout(frame)
        layout.setSpacing(15)

        # شريط البحث
        search_layout = QHBoxLayout()

        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                min-width: 60px;
            }
        """)
        search_layout.addWidget(search_label)

        self.address_search_input = QLineEdit()
        self.address_search_input.setPlaceholderText("ابحث بالاسم أو رقم الحساب أو البنك...")
        self.address_search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)
        self.address_search_input.textChanged.connect(self.search_address_book)
        search_layout.addWidget(self.address_search_input)

        # زر مسح البحث
        clear_search_btn = QPushButton("🗑️ مسح")
        clear_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        clear_search_btn.clicked.connect(lambda: self.address_search_input.clear())
        search_layout.addWidget(clear_search_btn)

        layout.addLayout(search_layout)

        # أزرار التحكم الرئيسية
        buttons_layout = QHBoxLayout()

        # زر تحديث القائمة
        refresh_btn = QPushButton("🔄 تحديث القائمة")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.load_address_book_data)
        buttons_layout.addWidget(refresh_btn)

        # زر تصدير البيانات
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        export_btn.clicked.connect(self.export_address_book)
        buttons_layout.addWidget(export_btn)

        # زر استيراد البيانات
        import_btn = QPushButton("📥 استيراد")
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        import_btn.clicked.connect(self.import_address_book)
        buttons_layout.addWidget(import_btn)

        buttons_layout.addStretch()

        # عداد العناوين
        self.address_count_label = QLabel("📊 العناوين: 0")
        self.address_count_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 6px;
            }
        """)
        buttons_layout.addWidget(self.address_count_label)

        layout.addLayout(buttons_layout)

        return frame

    def create_address_book_form(self):
        """إنشاء نموذج دفتر العناوين مبسط وواضح"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #27ae60;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout(frame)
        layout.setSpacing(15)

        # عنوان النموذج مع أيقونة
        header_layout = QHBoxLayout()

        form_title = QLabel("➕ إدارة العناوين")
        form_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #27ae60;
                margin-bottom: 5px;
            }
        """)
        header_layout.addWidget(form_title)

        header_layout.addStretch()

        # مؤشر حالة النموذج
        self.form_status_label = QLabel("📝 إضافة عنوان جديد")
        self.form_status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                font-style: italic;
            }
        """)
        header_layout.addWidget(self.form_status_label)

        layout.addLayout(header_layout)

        # تخطيط عمودي بسيط للحقول مع تسميات واضحة
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)

        # الصف الأول - الاسم ورقم الحساب
        row1_layout = QHBoxLayout()

        # الاسم الكامل
        name_group = QVBoxLayout()
        name_label = QLabel("👤 الاسم الكامل:")
        name_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        name_group.addWidget(name_label)
        self.ab_receiver_name_input = QLineEdit()
        self.ab_receiver_name_input.setPlaceholderText("أدخل الاسم الكامل...")
        self.ab_receiver_name_input.setMinimumHeight(35)
        self.ab_receiver_name_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        name_group.addWidget(self.ab_receiver_name_input)
        row1_layout.addLayout(name_group)

        # رقم الحساب
        account_group = QVBoxLayout()
        account_label = QLabel("🏦 رقم الحساب:")
        account_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        account_group.addWidget(account_label)
        self.ab_receiver_account_input = QLineEdit()
        self.ab_receiver_account_input.setPlaceholderText("أدخل رقم الحساب...")
        self.ab_receiver_account_input.setMinimumHeight(35)
        self.ab_receiver_account_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        account_group.addWidget(self.ab_receiver_account_input)
        row1_layout.addLayout(account_group)

        form_layout.addLayout(row1_layout)

        # الصف الثاني - البنك وفرع البنك
        row2_layout = QHBoxLayout()

        # اسم البنك
        bank_group = QVBoxLayout()
        bank_label = QLabel("🏛️ اسم البنك:")
        bank_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        bank_group.addWidget(bank_label)
        self.ab_receiver_bank_input = QLineEdit()
        self.ab_receiver_bank_input.setPlaceholderText("أدخل اسم البنك...")
        self.ab_receiver_bank_input.setMinimumHeight(35)
        self.ab_receiver_bank_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        bank_group.addWidget(self.ab_receiver_bank_input)
        row2_layout.addLayout(bank_group)

        # فرع البنك
        branch_group = QVBoxLayout()
        branch_label = QLabel("🏢 فرع البنك:")
        branch_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        branch_group.addWidget(branch_label)
        self.ab_receiver_bank_branch_input = QLineEdit()
        self.ab_receiver_bank_branch_input.setPlaceholderText("أدخل فرع البنك...")
        self.ab_receiver_bank_branch_input.setMinimumHeight(35)
        self.ab_receiver_bank_branch_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        branch_group.addWidget(self.ab_receiver_bank_branch_input)
        row2_layout.addLayout(branch_group)

        form_layout.addLayout(row2_layout)

        # الصف الثالث - البلد وبلد البنك
        row3_layout = QHBoxLayout()

        # البلد
        country_group = QVBoxLayout()
        country_label = QLabel("🌍 البلد:")
        country_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        country_group.addWidget(country_label)
        self.ab_receiver_country_input = QLineEdit()
        self.ab_receiver_country_input.setPlaceholderText("أدخل البلد...")
        self.ab_receiver_country_input.setMinimumHeight(35)
        self.ab_receiver_country_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        country_group.addWidget(self.ab_receiver_country_input)
        row3_layout.addLayout(country_group)

        # بلد البنك
        bank_country_group = QVBoxLayout()
        bank_country_label = QLabel("🏦 بلد البنك:")
        bank_country_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        bank_country_group.addWidget(bank_country_label)
        self.ab_receiver_bank_country_input = QLineEdit()
        self.ab_receiver_bank_country_input.setPlaceholderText("أدخل بلد البنك...")
        self.ab_receiver_bank_country_input.setMinimumHeight(35)
        self.ab_receiver_bank_country_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        bank_country_group.addWidget(self.ab_receiver_bank_country_input)
        row3_layout.addLayout(bank_country_group)

        form_layout.addLayout(row3_layout)

        # الصف الرابع - السويفت والعنوان
        row4_layout = QHBoxLayout()

        # السويفت
        swift_group = QVBoxLayout()
        swift_label = QLabel("💳 رمز السويفت:")
        swift_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        swift_group.addWidget(swift_label)
        self.ab_receiver_swift_input = QLineEdit()
        self.ab_receiver_swift_input.setPlaceholderText("أدخل رمز السويفت...")
        self.ab_receiver_swift_input.setMinimumHeight(35)
        self.ab_receiver_swift_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        swift_group.addWidget(self.ab_receiver_swift_input)
        row4_layout.addLayout(swift_group)

        # العنوان
        address_group = QVBoxLayout()
        address_label = QLabel("📍 العنوان:")
        address_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                font-size: 12px;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        address_group.addWidget(address_label)
        self.ab_receiver_address_input = QLineEdit()
        self.ab_receiver_address_input.setPlaceholderText("أدخل العنوان...")
        self.ab_receiver_address_input.setMinimumHeight(35)
        self.ab_receiver_address_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        address_group.addWidget(self.ab_receiver_address_input)
        row4_layout.addLayout(address_group)

        form_layout.addLayout(row4_layout)

        layout.addLayout(form_layout)

        # أزرار التحكم في النموذج
        form_buttons_layout = QHBoxLayout()
        form_buttons_layout.setSpacing(10)

        # زر حفظ/تحديث
        self.save_address_btn = QPushButton("💾 حفظ عنوان جديد")
        self.save_address_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
                transform: translateY(-1px);
            }
        """)
        self.save_address_btn.clicked.connect(self.save_address_book_entry)
        form_buttons_layout.addWidget(self.save_address_btn)

        # زر مسح النموذج
        clear_form_btn = QPushButton("🧹 مسح النموذج")
        clear_form_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        clear_form_btn.clicked.connect(self.clear_address_book_form)
        form_buttons_layout.addWidget(clear_form_btn)

        # زر إلغاء التعديل
        self.cancel_edit_btn = QPushButton("❌ إلغاء التعديل")
        self.cancel_edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.cancel_edit_btn.clicked.connect(self.cancel_address_edit)
        self.cancel_edit_btn.setVisible(False)  # مخفي افتراضياً
        form_buttons_layout.addWidget(self.cancel_edit_btn)

        form_buttons_layout.addStretch()

        # زر نسخ إلى نموذج الحوالة
        copy_to_form_btn = QPushButton("📋 نسخ للحوالة")
        copy_to_form_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        copy_to_form_btn.clicked.connect(self.copy_form_to_remittance)
        form_buttons_layout.addWidget(copy_to_form_btn)

        layout.addLayout(form_buttons_layout)

        return frame

    def cancel_address_edit(self):
        """إلغاء تعديل العنوان"""
        self.clear_address_book_form()
        self.form_status_label.setText("📝 إضافة عنوان جديد")
        self.save_address_btn.setText("💾 حفظ عنوان جديد")
        self.cancel_edit_btn.setVisible(False)
        self.current_editing_address_id = None

    def copy_form_to_remittance(self):
        """نسخ بيانات النموذج إلى نموذج الحوالة"""
        try:
            # نسخ البيانات من نموذج دفتر العناوين إلى نموذج الحوالة
            self.receiver_name_input.setText(self.ab_receiver_name_input.text())
            self.receiver_account_input.setText(self.ab_receiver_account_input.text())
            self.receiver_bank_input.setText(self.ab_receiver_bank_input.text())
            self.receiver_bank_branch_input.setText(self.ab_receiver_bank_branch_input.text())
            self.receiver_swift_input.setText(self.ab_receiver_swift_input.text())
            self.receiver_country_input.setText(self.ab_receiver_country_input.text())
            self.receiver_bank_country_input.setText(self.ab_receiver_bank_country_input.text())
            self.receiver_address_input.setText(self.ab_receiver_address_input.text())

            # الانتقال إلى تبويب طلب حوالة جديد
            self.tab_widget.setCurrentIndex(1)

            QMessageBox.information(self, "نجح", "تم نسخ البيانات إلى نموذج الحوالة بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في نسخ البيانات:\n{str(e)}")

    def export_address_book(self):
        """تصدير دفتر العناوين"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import csv

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير دفتر العناوين",
                f"address_book_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                db_path = Path("data/proshipment.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT receiver_name, receiver_account, receiver_bank, receiver_bank_branch,
                           receiver_swift, receiver_country, receiver_bank_country, receiver_address
                    FROM address_book WHERE is_active = 1
                    ORDER BY receiver_name
                """)

                addresses = cursor.fetchall()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['الاسم', 'رقم الحساب', 'البنك', 'فرع البنك',
                                   'السويفت', 'البلد', 'بلد البنك', 'العنوان'])
                    writer.writerows(addresses)

                conn.close()
                QMessageBox.information(self, "نجح", f"تم تصدير {len(addresses)} عنوان بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير البيانات:\n{str(e)}")

    def import_address_book(self):
        """استيراد دفتر العناوين"""
        try:
            from PySide6.QtWidgets import QFileDialog
            import csv

            # اختيار الملف
            file_path, _ = QFileDialog.getOpenFileName(
                self, "استيراد دفتر العناوين", "",
                "CSV Files (*.csv)"
            )

            if file_path:
                imported_count = 0

                with open(file_path, 'r', encoding='utf-8') as csvfile:
                    reader = csv.reader(csvfile)
                    next(reader)  # تخطي العنوان

                    db_path = Path("data/proshipment.db")
                    conn = sqlite3.connect(str(db_path))
                    cursor = conn.cursor()

                    for row in reader:
                        if len(row) >= 8:
                            cursor.execute("""
                                INSERT OR IGNORE INTO address_book
                                (receiver_name, receiver_account, receiver_bank, receiver_bank_branch,
                                 receiver_swift, receiver_country, receiver_bank_country, receiver_address)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            """, row[:8])
                            imported_count += 1

                    conn.commit()
                    conn.close()

                self.load_address_book_data()
                QMessageBox.information(self, "نجح", f"تم استيراد {imported_count} عنوان بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في استيراد البيانات:\n{str(e)}")

    def create_address_book_table(self):
        """إنشاء جدول دفتر العناوين"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        layout = QVBoxLayout(frame)

        # عنوان الجدول
        table_title = QLabel("📋 قائمة العناوين المحفوظة")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(table_title)

        # الجدول
        self.address_book_table = QTableWidget()
        self.address_book_table.setColumnCount(9)
        self.address_book_table.setHorizontalHeaderLabels([
            "الاسم", "رقم الحساب", "البنك", "الفرع",
            "السويفت", "البلد", "بلد البنك", "العنوان", "العمليات"
        ])

        # تنسيق الجدول
        self.address_book_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        self.address_book_table.setAlternatingRowColors(True)
        self.address_book_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.address_book_table.horizontalHeader().setStretchLastSection(True)
        self.address_book_table.setMinimumHeight(300)

        layout.addWidget(self.address_book_table)

        # تحميل البيانات
        self.load_address_book_data()

        return frame

    def create_quick_stats(self):
        """إنشاء إحصائيات سريعة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 20px;
            }
        """)

        layout = QGridLayout(frame)
        layout.setSpacing(20)

        # إجمالي الطلبات
        total_card = self.create_stat_card("📋", "إجمالي الطلبات", "0", "#3498db")
        layout.addWidget(total_card, 0, 0)

        # الطلبات المعلقة
        pending_card = self.create_stat_card("⏳", "طلبات معلقة", "0", "#f39c12")
        layout.addWidget(pending_card, 0, 1)

        # الطلبات المؤكدة
        confirmed_card = self.create_stat_card("✅", "طلبات مؤكدة", "0", "#27ae60")
        layout.addWidget(confirmed_card, 0, 2)

        # الطلبات المرفوضة
        rejected_card = self.create_stat_card("❌", "طلبات مرفوضة", "0", "#e74c3c")
        layout.addWidget(rejected_card, 0, 3)

        return frame

    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 15px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                color: {color};
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(icon_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
                margin-bottom: 5px;
            }}
        """)
        layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)

        return card

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #34495e;
                border: none;
                spacing: 10px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QToolBar QToolButton:hover {
                background-color: #3498db;
            }
        """)

        # إنشاء طلب جديد
        new_request_action = QAction("➕ طلب جديد", self)
        new_request_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        toolbar.addAction(new_request_action)

        toolbar.addSeparator()

        # تحديث البيانات
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.triggered.connect(self.load_requests)
        toolbar.addAction(refresh_action)

        # تصدير البيانات
        export_action = QAction("📤 تصدير", self)
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        toolbar.addSeparator()

        # الإعدادات
        settings_action = QAction("⚙️ الإعدادات", self)
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)

        toolbar.addSeparator()

        # زر الخروج
        exit_action = QAction("🚪 خروج", self)
        exit_action.triggered.connect(self.close_application)
        toolbar.addAction(exit_action)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495e;
                color: white;
                border-top: 1px solid #2c3e50;
                padding: 5px;
            }
        """)

        # رسالة الحالة
        self.status_bar.showMessage("جاهز - نظام طلب الحوالات")

    def setup_connections(self):
        """إعداد الاتصالات والإشارات"""
        # ربط تغيير التبويب بتحديث البيانات
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # ربط اختيار صف في الجدول
        self.requests_table.itemSelectionChanged.connect(self.on_request_selected)

        # ربط النقر المزدوج لفتح الطلب للتعديل
        self.requests_table.itemDoubleClicked.connect(self.edit_selected_request)

    def apply_modern_style(self):
        """تطبيق الستايل العصري"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QLineEdit, QComboBox, QSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border-color: #3498db;
            }
            QLabel {
                color: #2c3e50;
                font-weight: 500;
            }
        """)

    def load_requests(self):
        """تحميل قائمة طلبات الحوالات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء الجدول إذا لم يكن موجوداً أو تحديثه
            self.create_or_update_table(cursor)

            # جلب البيانات مع التحقق من وجود الأعمدة
            try:
                cursor.execute("""
                    SELECT id, request_number, sender_name, receiver_name, amount,
                           source_currency,
                           CASE WHEN receiver_country IS NOT NULL THEN receiver_country ELSE '' END as receiver_country,
                           created_at, status, priority
                    FROM remittance_requests
                    ORDER BY created_at DESC
                """)
            except sqlite3.OperationalError:
                # إذا فشل الاستعلام، استخدم الأعمدة الأساسية فقط
                cursor.execute("""
                    SELECT id, request_number, sender_name, receiver_name, amount,
                           source_currency, '' as receiver_country, created_at, status,
                           CASE WHEN priority IS NOT NULL THEN priority ELSE 'عادي' END as priority
                    FROM remittance_requests
                    ORDER BY created_at DESC
                """)

            requests = cursor.fetchall()
            self.current_requests = requests

            # ملء الجدول
            self.populate_requests_table(requests)

            conn.close()

            # تحديث شريط الحالة
            self.status_bar.showMessage(f"تم تحميل {len(requests)} طلب حوالة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات:\n{str(e)}")

    def create_or_update_table(self, cursor):
        """إنشاء أو تحديث جدول طلبات الحوالة"""
        try:
            # إنشاء الجدول الأساسي إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS remittance_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    request_number TEXT UNIQUE NOT NULL,
                    sender_name TEXT NOT NULL,
                    receiver_name TEXT NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    source_currency TEXT NOT NULL,
                    target_currency TEXT NOT NULL,
                    status TEXT DEFAULT 'معلق',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # قائمة الأعمدة الجديدة التي نريد إضافتها
            new_columns = [
                ("sender_phone", "TEXT"),
                ("sender_id", "TEXT"),
                ("sender_entity", "TEXT"),
                ("sender_fax", "TEXT"),
                ("sender_mobile", "TEXT"),
                ("sender_pobox", "TEXT"),
                ("sender_email", "TEXT"),
                ("sender_address", "TEXT"),
                ("receiver_phone", "TEXT"),
                ("receiver_id", "TEXT"),
                ("receiver_country", "TEXT"),
                ("receiver_city", "TEXT"),
                ("receiver_address", "TEXT"),
                ("receiver_account", "TEXT"),
                ("receiver_bank", "TEXT"),
                ("receiver_bank_branch", "TEXT"),
                ("receiver_swift", "TEXT"),
                ("receiver_bank_country", "TEXT"),
                ("exchange_rate", "DECIMAL(10,4) DEFAULT 1.0000"),
                ("sender_bank", "TEXT"),
                ("transfer_date", "DATE"),
                ("priority", "TEXT DEFAULT 'عادي'"),
                ("notes", "TEXT"),
                ("sms_notification", "BOOLEAN DEFAULT 1"),
                ("email_notification", "BOOLEAN DEFAULT 0"),
                ("auto_create_remittance", "BOOLEAN DEFAULT 1"),
                ("updated_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
                ("branch", "TEXT"),
                ("branch_id", "INTEGER"),
                ("exchanger", "TEXT"),
                ("exchanger_id", "INTEGER"),
                ("remittance_amount", "DECIMAL(15,2)"),
                ("currency", "TEXT"),
                ("currency_code", "TEXT"),
                ("transfer_purpose", "TEXT")
            ]

            # فحص الأعمدة الموجودة
            cursor.execute("PRAGMA table_info(remittance_requests)")
            existing_columns = [column[1] for column in cursor.fetchall()]

            # إضافة الأعمدة المفقودة
            for column_name, column_type in new_columns:
                if column_name not in existing_columns:
                    try:
                        cursor.execute(f"ALTER TABLE remittance_requests ADD COLUMN {column_name} {column_type}")
                        print(f"تم إضافة العمود: {column_name}")
                    except sqlite3.OperationalError as e:
                        print(f"خطأ في إضافة العمود {column_name}: {e}")

        except Exception as e:
            print(f"خطأ في إنشاء/تحديث الجدول: {e}")

    def populate_requests_table(self, requests):
        """ملء جدول الطلبات"""
        self.requests_table.setRowCount(len(requests))

        for row, request in enumerate(requests):
            # رقم الطلب
            item = QTableWidgetItem(str(request[1]))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 0, item)

            # اسم المرسل
            item = QTableWidgetItem(str(request[2]))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 1, item)

            # اسم المستقبل
            item = QTableWidgetItem(str(request[3]))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 2, item)

            # المبلغ
            item = QTableWidgetItem(f"{request[4]:.2f}")
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 3, item)

            # العملة
            item = QTableWidgetItem(str(request[5]))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 4, item)

            # البلد المستقبل
            item = QTableWidgetItem(str(request[6] or ""))
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 5, item)

            # تاريخ الطلب
            date_str = request[7][:10] if request[7] else ""
            item = QTableWidgetItem(date_str)
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            self.requests_table.setItem(row, 6, item)

            # الحالة
            status_item = QTableWidgetItem(str(request[8]))
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            if request[8] == "معلق":
                status_item.setBackground(Qt.yellow)
            elif request[8] == "مؤكد":
                status_item.setBackground(Qt.green)
            elif request[8] == "مرفوض":
                status_item.setBackground(Qt.red)
            self.requests_table.setItem(row, 7, status_item)

            # الأولوية
            priority_item = QTableWidgetItem(str(request[9]))
            priority_item.setFlags(priority_item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
            if request[9] == "طارئ":
                priority_item.setBackground(Qt.red)
            elif request[9] == "عاجل":
                priority_item.setBackground(Qt.yellow)
            self.requests_table.setItem(row, 8, priority_item)

            # أزرار الإجراءات
            actions_btn = QPushButton("⚙️ إجراءات")
            actions_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            self.requests_table.setCellWidget(row, 9, actions_btn)

    def load_filters_data(self):
        """تحميل بيانات المرشحات من إدارة البنوك وإعدادات النظام"""
        print("📊 تحميل بيانات النموذج من قاعدة البيانات...")
        self.load_currencies_from_system_settings()
        self.load_branches_from_bank_management()
        self.load_exchangers_from_bank_management()
        self.load_countries_data()
        print("✅ تم تحميل جميع البيانات بنجاح")

    def load_currencies_from_system_settings(self):
        """تحميل العملات من جدول العملات الرئيسي"""
        print("💱 تحميل العملات من جدول العملات...")
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل العملات من جدول currencies الصحيح
            try:
                cursor.execute("""
                    SELECT code, name, symbol, exchange_rate
                    FROM currencies
                    WHERE is_active = 1
                    ORDER BY name
                """)
                currencies = cursor.fetchall()

                if not currencies:
                    # إنشاء عملات افتراضية إذا لم توجد بيانات
                    self.create_default_currencies_in_currencies_table(cursor)
                    conn.commit()
                    cursor.execute("""
                        SELECT code, name, symbol, exchange_rate
                        FROM currencies
                        WHERE is_active = 1
                        ORDER BY name
                    """)
                    currencies = cursor.fetchall()

            except sqlite3.OperationalError as e:
                print(f"خطأ في الوصول لجدول currencies: {e}")
                # في حالة عدم وجود الجدول، إنشاؤه
                self.create_currencies_table_correct(cursor)
                self.create_default_currencies_in_currencies_table(cursor)
                conn.commit()
                cursor.execute("""
                    SELECT code, name, symbol, exchange_rate
                    FROM currencies
                    WHERE is_active = 1
                    ORDER BY name
                """)
                currencies = cursor.fetchall()

            # تنظيف قائمة العملة
            self.currency_combo.clear()

            # إضافة خيار افتراضي
            self.currency_combo.addItem("اختر العملة...", None)

            # إضافة العملات
            for currency in currencies:
                code = currency[0]
                name = currency[1] if currency[1] else code
                symbol = currency[2] if len(currency) > 2 and currency[2] else code

                display_text = f"{name} ({symbol})"
                self.currency_combo.addItem(display_text, code)

            # تعيين الريال السعودي كافتراضي (أو أول عملة متوفرة)
            default_found = False
            for i in range(self.currency_combo.count()):
                item_data = self.currency_combo.itemData(i)
                if item_data in ["SAR", "USD"]:  # البحث عن الريال السعودي أو الدولار
                    self.currency_combo.setCurrentIndex(i)
                    default_found = True
                    break

            if not default_found and self.currency_combo.count() > 1:
                self.currency_combo.setCurrentIndex(1)  # اختيار أول عملة

            conn.close()
            print(f"✅ تم تحميل {len(currencies)} عملة من جدول currencies")

        except Exception as e:
            print(f"❌ خطأ في تحميل العملات: {e}")
            # في حالة الخطأ، إضافة عملات افتراضية
            self.load_default_currencies()

    def create_system_currencies_table(self, cursor):
        """إنشاء جدول العملات في إعدادات النظام"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                currency_code TEXT UNIQUE NOT NULL,
                currency_name TEXT NOT NULL,
                currency_symbol TEXT,
                exchange_rate REAL DEFAULT 1.0,
                country TEXT,
                is_base_currency INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_currencies_table_correct(self, cursor):
        """إنشاء جدول العملات الصحيح"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code VARCHAR(3) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                name_en VARCHAR(100),
                symbol VARCHAR(10),
                exchange_rate FLOAT DEFAULT 1.0,
                is_base BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_default_currencies_in_currencies_table(self, cursor):
        """إنشاء عملات افتراضية في جدول currencies"""
        default_currencies = [
            ('SAR', 'ريال سعودي', 'Saudi Riyal', 'ر.س', 1.0, 1),
            ('USD', 'دولار أمريكي', 'US Dollar', '$', 0.27, 0),
            ('EUR', 'يورو', 'Euro', '€', 0.25, 0),
            ('AED', 'درهم إماراتي', 'UAE Dirham', 'د.إ', 0.98, 0),
            ('QAR', 'ريال قطري', 'Qatari Riyal', 'ر.ق', 0.97, 0),
            ('KWD', 'دينار كويتي', 'Kuwaiti Dinar', 'د.ك', 0.08, 0),
            ('BHD', 'دينار بحريني', 'Bahraini Dinar', 'د.ب', 0.10, 0),
            ('OMR', 'ريال عماني', 'Omani Rial', 'ر.ع', 0.10, 0),
            ('JOD', 'دينار أردني', 'Jordanian Dinar', 'د.أ', 0.19, 0),
            ('EGP', 'جنيه مصري', 'Egyptian Pound', 'ج.م', 13.15, 0)
        ]

        for code, name, name_en, symbol, rate, is_base in default_currencies:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO currencies
                    (code, name, name_en, symbol, exchange_rate, is_base, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, 1)
                """, (code, name, name_en, symbol, rate, is_base))
            except Exception as e:
                print(f"خطأ في إضافة العملة {code}: {e}")

    def create_currencies_table(self, cursor):
        """إنشاء جدول العملات البديل (قديم - للتوافق)"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                symbol TEXT,
                exchange_rate REAL DEFAULT 1.0,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_default_currencies_for_system(self, cursor):
        """إنشاء العملات الافتراضية لإعدادات النظام"""
        default_currencies = [
            ('YER', 'ريال يمني', 'ر.ي', 1.0, 'اليمن', 1),
            ('SAR', 'ريال سعودي', 'ر.س', 0.15, 'السعودية', 0),
            ('USD', 'دولار أمريكي', '$', 0.004, 'الولايات المتحدة', 0),
            ('EUR', 'يورو', '€', 0.0037, 'الاتحاد الأوروبي', 0),
            ('AED', 'درهم إماراتي', 'د.إ', 0.015, 'الإمارات', 0),
            ('QAR', 'ريال قطري', 'ر.ق', 0.015, 'قطر', 0),
            ('KWD', 'دينار كويتي', 'د.ك', 0.0012, 'الكويت', 0),
            ('BHD', 'دينار بحريني', 'د.ب', 0.0015, 'البحرين', 0),
            ('OMR', 'ريال عماني', 'ر.ع', 0.0015, 'عمان', 0),
            ('JOD', 'دينار أردني', 'د.أ', 0.003, 'الأردن', 0),
            ('EGP', 'جنيه مصري', 'ج.م', 0.12, 'مصر', 0),
            ('GBP', 'جنيه إسترليني', '£', 0.003, 'بريطانيا', 0),
            ('JPY', 'ين ياباني', '¥', 0.6, 'اليابان', 0),
            ('CNY', 'يوان صيني', '¥', 0.029, 'الصين', 0),
            ('TRY', 'ليرة تركية', '₺', 0.12, 'تركيا', 0)
        ]

        for code, name, symbol, rate, country, is_base in default_currencies:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO system_currencies
                    (currency_code, currency_name, currency_symbol, exchange_rate, country, is_base_currency, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, 1)
                """, (code, name, symbol, rate, country, is_base))
            except sqlite3.IntegrityError:
                pass  # العملة موجودة بالفعل

    def create_default_currencies(self, cursor):
        """إنشاء العملات الافتراضية للجدول البديل"""
        default_currencies = [
            ('YER', 'ريال يمني', 'ر.ي', 1.0),
            ('SAR', 'ريال سعودي', 'ر.س', 0.15),
            ('USD', 'دولار أمريكي', '$', 0.004),
            ('EUR', 'يورو', '€', 0.0037),
            ('AED', 'درهم إماراتي', 'د.إ', 0.015),
            ('QAR', 'ريال قطري', 'ر.ق', 0.015),
            ('KWD', 'دينار كويتي', 'د.ك', 0.0012),
            ('BHD', 'دينار بحريني', 'د.ب', 0.0015),
            ('OMR', 'ريال عماني', 'ر.ع', 0.0015),
            ('JOD', 'دينار أردني', 'د.أ', 0.003),
            ('EGP', 'جنيه مصري', 'ج.م', 0.12),
            ('GBP', 'جنيه إسترليني', '£', 0.003)
        ]

        for code, name, symbol, rate in default_currencies:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO currencies (code, name, symbol, exchange_rate, is_active)
                    VALUES (?, ?, ?, ?, 1)
                """, (code, name, symbol, rate))
            except sqlite3.IntegrityError:
                pass  # العملة موجودة بالفعل

    def load_default_currencies(self):
        """تحميل عملات افتراضية في حالة الخطأ"""
        self.currency_combo.clear()
        self.currency_combo.addItem("اختر العملة...", None)

        default_currencies = [
            ("YER", "ريال يمني (ر.ي)"),
            ("SAR", "ريال سعودي (ر.س)"),
            ("USD", "دولار أمريكي ($)"),
            ("EUR", "يورو (€)"),
            ("AED", "درهم إماراتي (د.إ)"),
            ("QAR", "ريال قطري (ر.ق)"),
            ("KWD", "دينار كويتي (د.ك)"),
            ("BHD", "دينار بحريني (د.ب)"),
            ("OMR", "ريال عماني (ر.ع)"),
            ("JOD", "دينار أردني (د.أ)"),
            ("EGP", "جنيه مصري (ج.م)"),
            ("GBP", "جنيه إسترليني (£)")
        ]

        for code, display in default_currencies:
            self.currency_combo.addItem(display, code)

        # تعيين الريال اليمني كافتراضي
        self.currency_combo.setCurrentIndex(1)  # الريال اليمني

    def load_branches_from_bank_management(self):
        """تحميل الفروع من إدارة البنوك والصرافين"""
        print("🏦 تحميل الفروع من إدارة البنوك...")
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الفروع من جدول إدارة البنوك والصرافين
            try:
                # البحث في جدول إدارة البنوك والصرافين
                cursor.execute("""
                    SELECT id, branch_name, address, phone, manager_name
                    FROM bank_branches
                    WHERE is_active = 1
                    ORDER BY branch_name
                """)
                branches = cursor.fetchall()

                if not branches:
                    # البحث في جدول بديل
                    cursor.execute("""
                        SELECT id, name, address, phone, manager_name
                        FROM branches
                        WHERE is_active = 1
                        ORDER BY name
                    """)
                    branches = cursor.fetchall()

                if not branches:
                    # إنشاء فروع افتراضية
                    self.create_default_branches_for_bank_management(cursor)
                    conn.commit()
                    cursor.execute("""
                        SELECT id, branch_name, address, phone, manager_name
                        FROM bank_branches
                        WHERE is_active = 1
                        ORDER BY branch_name
                    """)
                    branches = cursor.fetchall()

            except sqlite3.OperationalError:
                # إنشاء الجدول والبيانات الافتراضية
                self.create_bank_branches_table_for_management(cursor)
                self.create_default_branches_for_bank_management(cursor)
                conn.commit()
                cursor.execute("""
                    SELECT id, branch_name, address, phone, manager_name
                    FROM bank_branches
                    WHERE is_active = 1
                    ORDER BY branch_name
                """)
                branches = cursor.fetchall()

            # تنظيف قائمة الفروع
            self.branch_combo.clear()
            self.branch_combo.addItem("اختر الفرع...", None)

            # إضافة الفروع مع معلومات تفصيلية
            for branch in branches:
                branch_name = branch[1] if len(branch) > 1 else f"فرع {branch[0]}"
                display_text = branch_name

                # إضافة العنوان إذا كان متوفراً
                if len(branch) > 2 and branch[2]:
                    display_text += f" - {branch[2]}"

                # إضافة اسم المدير إذا كان متوفراً
                if len(branch) > 4 and branch[4]:
                    display_text += f" (مدير: {branch[4]})"

                self.branch_combo.addItem(display_text, branch[0])

            conn.close()
            print(f"✅ تم تحميل {len(branches)} فرع من إدارة البنوك")

        except Exception as e:
            print(f"❌ خطأ في تحميل الفروع: {e}")
            # في حالة الخطأ، إضافة فروع افتراضية
            self.load_default_branches()

    def load_exchangers_from_bank_management(self):
        """تحميل الصرافين من إدارة البنوك والصرافين"""
        print("👤 تحميل الصرافين من إدارة البنوك...")

        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # تحميل الصرافين من جدول exchanges الصحيح
            try:
                cursor.execute("""
                    SELECT id, name, phone, email, license_number, address, category
                    FROM exchanges
                    WHERE is_active = 1
                    ORDER BY name
                """)
                exchangers = cursor.fetchall()

                if not exchangers:
                    # إنشاء صرافين افتراضيين إذا لم توجد بيانات
                    self.create_default_exchangers_in_exchanges_table(cursor)
                    conn.commit()
                    cursor.execute("""
                        SELECT id, name, phone, email, license_number, address, category
                        FROM exchanges
                        WHERE is_active = 1
                        ORDER BY name
                    """)
                    exchangers = cursor.fetchall()

            except sqlite3.OperationalError as e:
                print(f"خطأ في الوصول لجدول exchanges: {e}")
                # في حالة عدم وجود الجدول، إنشاؤه
                self.create_exchanges_table(cursor)
                self.create_default_exchangers_in_exchanges_table(cursor)
                conn.commit()
                cursor.execute("""
                    SELECT id, name, phone, email, license_number, address, category
                    FROM exchanges
                    WHERE is_active = 1
                    ORDER BY name
                """)
                exchangers = cursor.fetchall()

            # تنظيف قائمة الصرافين
            self.exchanger_combo.clear()
            self.exchanger_combo.addItem("اختر الصراف...", None)

            # إضافة الصرافين مع معلومات تفصيلية
            for exchanger in exchangers:
                exchanger_id = exchanger[0]
                exchanger_name = exchanger[1] if exchanger[1] else f"صراف {exchanger_id}"
                phone = exchanger[2] if len(exchanger) > 2 and exchanger[2] else ""
                email = exchanger[3] if len(exchanger) > 3 and exchanger[3] else ""
                license_number = exchanger[4] if len(exchanger) > 4 and exchanger[4] else ""
                address = exchanger[5] if len(exchanger) > 5 and exchanger[5] else ""
                category = exchanger[6] if len(exchanger) > 6 and exchanger[6] else ""

                # بناء النص المعروض
                display_text = exchanger_name

                # إضافة الفئة إذا كانت متوفرة
                if category:
                    display_text += f" ({category})"

                # إضافة رقم الترخيص إذا كان متوفراً
                if license_number:
                    display_text += f" - ترخيص: {license_number}"

                # إضافة رقم الهاتف إذا كان متوفراً
                if phone:
                    display_text += f" - {phone}"

                self.exchanger_combo.addItem(display_text, exchanger_id)

            conn.close()
            print(f"✅ تم تحميل {len(exchangers)} صراف من جدول exchanges")

        except Exception as e:
            print(f"❌ خطأ في تحميل الصرافين: {e}")
            # في حالة الخطأ، إضافة صرافين افتراضيين
            self.load_default_exchangers()

    def create_bank_branches_table_for_management(self, cursor):
        """إنشاء جدول فروع البنوك لإدارة البنوك والصرافين"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS bank_branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                branch_name TEXT NOT NULL,
                address TEXT,
                phone TEXT,
                email TEXT,
                manager_name TEXT,
                city TEXT,
                region TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_exchanges_table(self, cursor):
        """إنشاء جدول الصرافين الصحيح"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS exchanges (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category TEXT,
                license_number TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                website TEXT,
                rating DECIMAL(2,1) DEFAULT 0.0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_default_exchangers_in_exchanges_table(self, cursor):
        """إنشاء صرافين افتراضيين في جدول exchanges"""
        default_exchangers = [
            ('شركة القاسمي للصرافة والتحويلات', 'صرافة', 'LIC001', '+967-1-123456', '<EMAIL>', 'صنعاء - شارع الزبيري'),
            ('شركة الحزمي للصرافة والتحويلات', 'صرافة', 'LIC002', '+967-1-234567', '<EMAIL>', 'صنعاء - شارع الستين'),
            ('شركة الحجري للصرافة والتحويلات', 'صرافة', 'LIC003', '+967-1-345678', '<EMAIL>', 'صنعاء - شارع الحدة'),
            ('مؤسسة الأمين للصرافة', 'مؤسسة', 'LIC004', '+967-1-456789', '<EMAIL>', 'صنعاء - شارع الثورة'),
            ('صرافة الوحدة', 'صرافة', 'LIC005', '+967-1-567890', '<EMAIL>', 'صنعاء - شارع الوحدة')
        ]

        for name, category, license_num, phone, email, address in default_exchangers:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO exchanges
                    (name, category, license_number, phone, email, address, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, 1)
                """, (name, category, license_num, phone, email, address))
            except Exception as e:
                print(f"خطأ في إضافة الصراف {name}: {e}")

    def create_exchangers_table_for_management(self, cursor):
        """إنشاء جدول الصرافين لإدارة البنوك والصرافين (قديم - للتوافق)"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS exchangers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchanger_name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                branch_id INTEGER,
                license_number TEXT,
                license_date TEXT,
                license_expiry TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (branch_id) REFERENCES bank_branches (id)
            )
        """)

    def create_default_branches_for_bank_management(self, cursor):
        """إنشاء فروع افتراضية لإدارة البنوك والصرافين"""
        default_branches = [
            ('الادارة العامة صنعاء', 'صنعاء، اليمن', '+967 1 234567', '<EMAIL>', 'مدير الادارة العامة', 'صنعاء', 'أمانة العاصمة'),
            ('فرع عدن', 'عدن، اليمن', '+967 2 456789', '<EMAIL>', 'مدير فرع عدن', 'عدن', 'محافظة عدن'),
            ('فرع المكلا', 'المكلا، حضرموت، اليمن', '+967 5 789012', '<EMAIL>', 'مدير فرع المكلا', 'المكلا', 'محافظة حضرموت')
        ]

        for branch_name, address, phone, email, manager, city, region in default_branches:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO bank_branches
                    (branch_name, address, phone, email, manager_name, city, region, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                """, (branch_name, address, phone, email, manager, city, region))
            except sqlite3.IntegrityError:
                pass  # الفرع موجود بالفعل

    def create_default_exchangers_for_bank_management(self, cursor):
        """إنشاء صرافين افتراضيين لإدارة البنوك والصرافين"""
        default_exchangers = [
            ('أحمد محمد الصراف', '+967 *********', '<EMAIL>', 'شارع الزبيري، صنعاء', 1, 'EX2024001', '2024-01-01', '2025-12-31'),
            ('علي سالم للصرافة', '+967 *********', '<EMAIL>', 'شارع الستين، صنعاء', 2, 'EX2024002', '2024-01-15', '2025-12-31'),
            ('محمد عبدالله للتحويل', '+967 *********', '<EMAIL>', 'شارع المعلا، عدن', 3, 'EX2024003', '2024-02-01', '2025-12-31'),
            ('سالم أحمد الصراف', '+967 *********', '<EMAIL>', 'شارع جمال، تعز', 4, 'EX2024004', '2024-02-15', '2025-12-31'),
            ('عبدالله محمد للصرافة', '+967 *********', '<EMAIL>', 'شارع الكورنيش، الحديدة', 5, 'EX2024005', '2024-03-01', '2025-12-31'),
            ('حسن علي للتحويلات', '+967 777678901', '<EMAIL>', 'شارع الثورة، إب', 6, '*********', '2024-03-15', '2025-12-31'),
            ('فاطمة أحمد للصرافة', '+967 777789012', '<EMAIL>', 'شارع الزبيري، صنعاء', 1, '*********', '2024-04-01', '2025-12-31'),
            ('خالد سالم للتحويلات', '+967 777890123', '<EMAIL>', 'شارع المعلا، عدن', 3, '*********', '2024-04-15', '2025-12-31')
        ]

        for exchanger_name, phone, email, address, branch_id, license_num, license_date, license_expiry in default_exchangers:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO exchangers
                    (exchanger_name, phone, email, address, branch_id, license_number, license_date, license_expiry, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                """, (exchanger_name, phone, email, address, branch_id, license_num, license_date, license_expiry))
            except sqlite3.IntegrityError:
                pass  # الصراف موجود بالفعل

    def load_default_branches(self):
        """تحميل فروع افتراضية في حالة الخطأ"""
        self.branch_combo.clear()
        self.branch_combo.addItem("اختر الفرع...", None)

        default_branches = [
            ("1", "الادارة العامة صنعاء"),
            ("2", "فرع عدن"),
            ("3", "فرع المكلا")
        ]

        for branch_id, name in default_branches:
            self.branch_combo.addItem(name, branch_id)

        print("⚠️ تم تحميل فروع افتراضية")

    def load_default_exchangers(self):
        """تحميل صرافين افتراضيين في حالة الخطأ"""
        self.exchanger_combo.clear()
        self.exchanger_combo.addItem("اختر الصراف...", None)

        default_exchangers = [
            ("1", "أحمد محمد الصراف (الفرع الرئيسي) - EX2024001"),
            ("2", "علي سالم للصرافة (فرع الشمال) - EX2024002"),
            ("3", "محمد عبدالله للتحويل (فرع عدن) - EX2024003"),
            ("4", "سالم أحمد الصراف (فرع تعز) - EX2024004"),
            ("5", "عبدالله محمد للصرافة (فرع الحديدة) - EX2024005"),
            ("6", "حسن علي للتحويلات (فرع إب) - *********"),
            ("7", "فاطمة أحمد للصرافة (الفرع الرئيسي) - *********"),
            ("8", "خالد سالم للتحويلات (فرع عدن) - *********")
        ]

        for exchanger_id, name in default_exchangers:
            self.exchanger_combo.addItem(name, exchanger_id)

        print("⚠️ تم تحميل صرافين افتراضيين")





    def load_countries_data(self):
        """تحميل قائمة البلدان"""
        countries = [
            "اليمن", "السعودية", "الإمارات", "قطر", "الكويت", "البحرين", "عمان",
            "الأردن", "لبنان", "سوريا", "العراق", "مصر", "السودان", "ليبيا",
            "المغرب", "الجزائر", "تونس", "موريتانيا", "الصومال", "جيبوتي",
            "أمريكا", "بريطانيا", "ألمانيا", "فرنسا", "إيطاليا", "هولندا",
            "السويد", "النرويج", "الدنمارك", "كندا", "أستراليا", "ماليزيا",
            "إندونيسيا", "تركيا", "الهند", "باكستان", "بنغلاديش", "الفلبين"
        ]

        # تم تحويل حقل البلد إلى حقل نصي - لا حاجة لتحميل قائمة البلدان
        pass

    # وظائف الأحداث والإجراءات
    def on_tab_changed(self, index):
        """معالج تغيير التبويب"""
        if index == 0:  # تبويب قائمة الطلبات
            self.load_requests()
        elif index == 2:  # تبويب التقارير
            self.update_statistics()

    def on_request_selected(self):
        """معالج اختيار طلب من الجدول"""
        selected_rows = self.requests_table.selectionModel().selectedRows()
        if selected_rows:
            row = selected_rows[0].row()
            if row < len(self.current_requests):
                self.selected_request_id = self.current_requests[row][0]
                print(f"📋 تم اختيار الطلب: {self.selected_request_id}")
            else:
                print(f"⚠️ صف غير صالح: {row}, العدد الكلي: {len(self.current_requests)}")
        else:
            print("⚠️ لم يتم اختيار أي صف")

    def filter_requests(self):
        """تطبيق المرشحات على الطلبات"""
        try:
            status_filter = self.status_filter.currentText()
            date_from = self.date_from_filter.date().toString("yyyy-MM-dd")
            date_to = self.date_to_filter.date().toString("yyyy-MM-dd")

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # استعلام آمن مع التحقق من وجود الأعمدة
            try:
                query = """
                    SELECT id, request_number, sender_name, receiver_name, amount,
                           source_currency,
                           CASE WHEN receiver_country IS NOT NULL THEN receiver_country ELSE '' END as receiver_country,
                           created_at, status,
                           CASE WHEN priority IS NOT NULL THEN priority ELSE 'عادي' END as priority
                    FROM remittance_requests
                    WHERE DATE(created_at) BETWEEN ? AND ?
                """
                params = [date_from, date_to]

                if status_filter != "جميع الحالات":
                    query += " AND status = ?"
                    params.append(status_filter)

                query += " ORDER BY created_at DESC"

                cursor.execute(query, params)
            except sqlite3.OperationalError:
                # استعلام بديل للجداول القديمة
                query = """
                    SELECT id, request_number, sender_name, receiver_name, amount,
                           source_currency, '' as receiver_country, created_at, status, 'عادي' as priority
                    FROM remittance_requests
                    WHERE DATE(created_at) BETWEEN ? AND ?
                """
                params = [date_from, date_to]

                if status_filter != "جميع الحالات":
                    query += " AND status = ?"
                    params.append(status_filter)

                query += " ORDER BY created_at DESC"

                cursor.execute(query, params)

            requests = cursor.fetchall()

            self.current_requests = requests
            self.populate_requests_table(requests)

            conn.close()

            self.status_bar.showMessage(f"تم العثور على {len(requests)} طلب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث:\n{str(e)}")

    def save_new_request(self):
        """حفظ طلب حوالة جديد أو تحديث طلب موجود (نظام الحالات المتقدم)"""
        if not self.validate_new_request_form():
            return

        try:
            # جمع البيانات
            request_data = self.collect_new_request_data()

            # التحقق من وضع النافذة
            if self.window_mode == "EDIT" and self.current_editing_request_id:
                # تحديث طلب موجود
                success = self.update_request_in_database(request_data, self.current_editing_request_id)

                if success:
                    QMessageBox.information(self, "نجح التحديث",
                                          f"تم تحديث طلب الحوالة بنجاح!\nرقم الطلب: {request_data['request_number']}")

                    # إعادة تعيين النظام للوضع المعطل
                    self.set_window_mode("DISABLED")

                    # تحديث القائمة
                    self.load_requests()

                    # الانتقال لتبويب القائمة
                    self.tab_widget.setCurrentIndex(0)
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في تحديث طلب الحوالة")

            elif self.window_mode == "NEW":
                # حفظ طلب جديد
                request_id = self.save_request_to_database(request_data)

                if request_id:
                    QMessageBox.information(self, "نجح الحفظ",
                                          f"تم حفظ طلب الحوالة بنجاح!\nرقم الطلب: {request_data['request_number']}")

                    # التحقق من إضافة المستقبل إلى دفتر العناوين
                    self.check_and_save_to_address_book(request_data)

                    # إرسال إشارة
                    request_data['id'] = request_id
                    self.remittance_request_created.emit(request_data)

                    # إعادة تعيين النظام للوضع المعطل
                    self.set_window_mode("DISABLED")

                    # تحديث القائمة
                    self.load_requests()

                    # الانتقال لتبويب القائمة
                    self.tab_widget.setCurrentIndex(0)
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حفظ طلب الحوالة")

            else:
                QMessageBox.warning(self, "تحذير", "النموذج غير مفعل. يرجى اختيار 'إضافة' أو 'تعديل' أولاً.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ الطلب:\n{str(e)}")



    def send_new_request_to_create_remittance(self):
        """إرسال طلب جديد لنافذة إنشاء الحوالة"""
        if not self.validate_new_request_form():
            return

        try:
            # جمع البيانات
            request_data = self.collect_new_request_data()

            # إرسال إشارة
            self.send_to_create_remittance.emit(request_data)

            QMessageBox.information(self, "تم الإرسال",
                                  "تم إرسال البيانات لنافذة إنشاء الحوالة بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إرسال البيانات:\n{str(e)}")

    def send_selected_to_create_remittance(self):
        """إرسال الطلب المختار لنافذة إنشاء الحوالة"""
        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        try:
            # جلب بيانات الطلب المختار
            request_data = self.get_request_data(self.selected_request_id)

            if request_data:
                # إرسال إشارة
                self.send_to_create_remittance.emit(request_data)

                QMessageBox.information(self, "تم الإرسال",
                                      "تم إرسال الطلب لنافذة إنشاء الحوالة بنجاح!")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في جلب بيانات الطلب")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إرسال الطلب:\n{str(e)}")

    # وظائف مساعدة
    def validate_new_request_form(self):
        """التحقق من صحة نموذج الطلب الجديد"""
        # التحقق من البيانات الأساسية
        amount_text = self.remittance_amount_input.text().strip()
        if not amount_text:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال مبلغ الحوالة")
            self.remittance_amount_input.setFocus()
            return False

        # التحقق من أن المبلغ رقم صالح
        try:
            amount_value = float(amount_text.replace(',', ''))
            if amount_value <= 0:
                QMessageBox.warning(self, "بيانات غير صحيحة", "يرجى إدخال مبلغ صحيح أكبر من صفر")
                self.remittance_amount_input.setFocus()
                return False
        except ValueError:
            QMessageBox.warning(self, "بيانات غير صحيحة", "يرجى إدخال مبلغ صحيح (أرقام فقط)")
            self.remittance_amount_input.setFocus()
            return False

        if not self.transfer_purpose_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال الغرض من التحويل")
            self.transfer_purpose_input.setFocus()
            return False

        # التحقق من معلومات المرسل
        if not self.sender_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المرسل")
            self.sender_name_input.setFocus()
            return False

        if not self.sender_phone_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال رقم هاتف المرسل")
            self.sender_phone_input.setFocus()
            return False

        # التحقق من معلومات المستقبل
        if not self.receiver_name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المستقبل")
            self.receiver_name_input.setFocus()
            return False

        if not self.receiver_account_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال رقم حساب المستقبل")
            self.receiver_account_input.setFocus()
            return False

        if not self.receiver_bank_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم البنك المستقبل")
            self.receiver_bank_input.setFocus()
            return False

        return True

    def collect_new_request_data(self):
        """جمع بيانات الطلب الجديد"""
        # استخدام رقم الطلب من الحقل (تم إنشاؤه تلقائياً)
        request_number = self.request_number_input.text().strip()

        return {
            'request_number': request_number,
            'request_date': self.request_date_input.date().toString("yyyy-MM-dd"),
            'branch': self.branch_combo.currentText(),
            'branch_id': self.branch_combo.currentData(),
            'exchanger': self.exchanger_combo.currentText(),
            'exchanger_id': self.exchanger_combo.currentData(),
            'remittance_amount': self.remittance_amount_input.text().strip(),
            'currency': self.currency_combo.currentText(),
            'currency_code': self.currency_combo.currentData(),
            'transfer_purpose': self.transfer_purpose_input.text().strip(),

            # معلومات المرسل المحدثة
            'sender_name': self.sender_name_input.text().strip(),
            'sender_entity': self.sender_entity_input.text().strip(),
            'sender_phone': self.sender_phone_input.text().strip(),
            'sender_fax': self.sender_fax_input.text().strip(),
            'sender_mobile': self.sender_mobile_input.text().strip(),
            'sender_pobox': self.sender_pobox_input.text().strip(),
            'sender_email': self.sender_email_input.text().strip(),
            'sender_address': self.sender_address_input.text().strip(),

            # معلومات المستقبل المحدثة
            'receiver_name': self.receiver_name_input.text().strip(),
            'receiver_account': self.receiver_account_input.text().strip(),
            'receiver_bank': self.receiver_bank_input.text().strip(),
            'receiver_bank_branch': self.receiver_bank_branch_input.text().strip(),
            'receiver_swift': self.receiver_swift_input.text().strip(),
            'receiver_country': self.receiver_country_input.text().strip(),
            'receiver_bank_country': self.receiver_bank_country_input.text().strip(),
            'receiver_address': self.receiver_address_input.text().strip(),

            # ملاحظات وخيارات
            'notes': self.notes_input.toPlainText().strip(),
            'sms_notification': self.sms_notification_check.isChecked(),
            'email_notification': self.email_notification_check.isChecked(),
            'auto_create_remittance': self.auto_create_remittance_check.isChecked(),
            'status': 'معلق',
            'created_at': datetime.now().isoformat()
        }

    def save_request_to_database(self, data):
        """حفظ الطلب في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التأكد من تحديث الجدول أولاً
            self.create_or_update_table(cursor)

            # استخدام الإدراج الديناميكي
            request_id = self.insert_request_dynamically(cursor, data)
            conn.commit()
            conn.close()

            return request_id

        except Exception as e:
            print(f"خطأ في حفظ الطلب: {e}")
            return None

    def get_request_data(self, request_id):
        """جلب بيانات طلب محدد"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM remittance_requests WHERE id = ?
            """, (request_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                # تحويل النتيجة إلى قاموس
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))

            return None

        except Exception as e:
            print(f"خطأ في جلب بيانات الطلب: {e}")
            return None

    def clear_form(self):
        """مسح نموذج الطلب الجديد"""
        # إعادة تعيين وضع التحرير
        self.editing_request_id = None
        self.cancel_edit_btn.setVisible(False)

        # مسح البيانات الأساسية
        self.request_date_input.setDate(QDate.currentDate())
        # إنشاء رقم طلب جديد
        auto_number = f"REQ{datetime.now().strftime('%Y%m%d%H%M%S')}"
        self.request_number_input.setText(auto_number)
        self.branch_combo.setCurrentIndex(0)
        self.exchanger_combo.setCurrentIndex(0)
        self.remittance_amount_input.clear()
        self.currency_combo.setCurrentIndex(0)
        self.transfer_purpose_input.setText("COST OF FOODSTUFF")  # إعادة تعيين القيمة الافتراضية

        # إعادة تعبئة معلومات المرسل بالبيانات الافتراضية
        self.sender_name_input.setText("ALFOGEHI FOR GENERAL TRADING AND SUPPLY CO., LTD")
        self.sender_entity_input.setText("G.M: NASHA'AT RASHAD QASIM ALDUBAEE")
        self.sender_phone_input.setText("+967 1 616109")
        self.sender_fax_input.setText("+967 1 615909")
        self.sender_mobile_input.setText("+967 *********")
        self.sender_pobox_input.setText("1903")
        self.sender_email_input.setText("<EMAIL>, <EMAIL>")
        self.sender_address_input.setText("TAIZ STREET, ORPHAN BUIDING, NEAR ALBRKA STORES – SANA'A, YEMEN")

        # مسح معلومات المستقبل
        self.receiver_name_input.clear()
        self.receiver_account_input.clear()
        self.receiver_bank_input.clear()
        self.receiver_bank_branch_input.clear()
        self.receiver_swift_input.clear()
        self.receiver_country_input.clear()
        self.receiver_bank_country_input.clear()
        self.receiver_address_input.clear()

        # مسح الملاحظات والخيارات
        self.notes_input.clear()
        self.sms_notification_check.setChecked(True)
        self.email_notification_check.setChecked(False)
        self.auto_create_remittance_check.setChecked(True)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        # هذه الدالة يمكن تطويرها لاحقاً لحساب الإحصائيات الفعلية
        pass

    # وظائف أخرى
    def view_request_details(self):
        """عرض تفاصيل الطلب"""
        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        try:
            # استيراد نافذة التفاصيل
            from .remittance_details_window import RemittanceDetailsWindow

            # إنشاء وفتح نافذة التفاصيل
            self.details_window = RemittanceDetailsWindow(self.selected_request_id, self)
            self.details_window.show()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة التفاصيل:\n{str(e)}")

    def update_request_status(self):
        """تحديث حالة الطلب"""
        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        # يمكن فتح نافذة تحديث الحالة
        QMessageBox.information(self, "تحديث الحالة", f"تحديث حالة الطلب رقم: {self.selected_request_id}")

    def delete_request(self):
        """حذف الطلب"""
        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   "هل أنت متأكد من حذف هذا الطلب؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                # تنفيذ الحذف الفعلي من قاعدة البيانات
                db_path = Path("data/proshipment.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("DELETE FROM remittance_requests WHERE id = ?", (self.selected_request_id,))
                conn.commit()
                conn.close()

                # إعادة تحميل قائمة الطلبات
                self.load_requests()

                # إعادة تعيين الطلب المختار
                self.selected_request_id = None

                QMessageBox.information(self, "تم الحذف", "تم حذف الطلب بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف الطلب:\n{str(e)}")

    def edit_selected_request(self):
        """تحرير الطلب المختار"""
        print(f"🔧 محاولة تحرير الطلب: {self.selected_request_id}")

        if not self.selected_request_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طلب من القائمة")
            return

        try:
            # تحميل بيانات الطلب من قاعدة البيانات
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إضافة العمود الجديد إذا لم يكن موجوداً
            try:
                cursor.execute("ALTER TABLE remittance_requests ADD COLUMN receiver_bank_country TEXT")
            except sqlite3.OperationalError:
                pass  # العمود موجود بالفعل

            cursor.execute("SELECT * FROM remittance_requests WHERE id = ?", (self.selected_request_id,))
            result = cursor.fetchone()

            if result:
                print(f"✅ تم العثور على الطلب في قاعدة البيانات")

                # الحصول على أسماء الأعمدة
                cursor.execute("PRAGMA table_info(remittance_requests)")
                columns = [column[1] for column in cursor.fetchall()]

                # تحويل النتيجة إلى قاموس
                request_data = dict(zip(columns, result))
                print(f"📋 بيانات الطلب: رقم {request_data.get('request_number', 'غير محدد')}")

                # تعبئة النموذج بالبيانات
                print("📝 تعبئة النموذج...")
                self.populate_form_for_editing(request_data)

                # التبديل إلى تبويب الطلب الجديد
                print("🔄 التبديل إلى تبويب الطلب الجديد...")
                self.tab_widget.setCurrentIndex(1)  # تبويب طلب حوالة جديد

                # حفظ معرف الطلب للتحديث
                self.editing_request_id = self.selected_request_id
                print(f"💾 تم حفظ معرف التحرير: {self.editing_request_id}")

                # إظهار زر إلغاء التحرير
                self.cancel_edit_btn.setVisible(True)
                print("🔘 تم إظهار زر إلغاء التحرير")

                # إظهار النافذة وجعلها في المقدمة
                print("🖥️ إظهار النافذة...")

                # إجبار إظهار النافذة بطرق متعددة
                self.setVisible(True)
                self.show()
                self.showNormal()

                # التأكد من أن النافذة مرئية
                if self.isMinimized():
                    print("📏 النافذة مصغرة، استعادة الحجم العادي...")
                    self.showNormal()

                if self.isHidden():
                    print("👁️ النافذة مخفية، إظهارها...")
                    self.show()
                    self.setVisible(True)

                # رفع النافذة للمقدمة وتفعيلها
                self.raise_()
                self.activateWindow()

                # تعيين النافذة كنافذة نشطة
                self.setWindowState(Qt.WindowActive)

                # محاولة إحضار النافذة للمقدمة بقوة
                try:
                    # للويندوز
                    import ctypes
                    from ctypes import wintypes
                    hwnd = int(self.winId())
                    ctypes.windll.user32.SetForegroundWindow(hwnd)
                    ctypes.windll.user32.BringWindowToTop(hwnd)
                    print("🪟 تم إحضار النافذة للمقدمة (Windows)")
                except:
                    print("⚠️ لم يتمكن من استخدام Windows API")

                # التركيز على الحقل الأول
                print("🎯 تركيز على الحقل الأول...")
                self.sender_name_input.setFocus()

                print("✅ تم إظهار النافذة بنجاح!")

                # إظهار رسالة نجاح بعد تأخير قصير
                from PySide6.QtCore import QTimer
                def show_success_message():
                    QMessageBox.information(self, "وضع التحرير",
                        f"تم تحميل الطلب رقم {request_data.get('request_number', self.selected_request_id)} في وضع التحرير.\n\n"
                        "✅ تم تحميل جميع البيانات\n"
                        "📝 يمكنك الآن تعديل البيانات\n"
                        "💾 اضغط 'حفظ التغييرات' عند الانتهاء\n"
                        "❌ أو 'إلغاء التحرير' للعودة")

                # تأخير الرسالة لضمان ظهور النافذة أولاً
                QTimer.singleShot(100, show_success_message)
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الطلب")

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الطلب للتحرير:\n{str(e)}")

    def populate_form_for_editing(self, request_data):
        """تعبئة النموذج للتحرير"""
        try:
            print(f"🔍 بدء تعبئة النموذج للتحرير...")
            print(f"📊 البيانات المتاحة: {list(request_data.keys())}")

            # تشخيص البيانات المحفوظة
            self.diagnose_saved_data(request_data)
            # البيانات الأساسية (مع معالجة شاملة للأخطاء)
            try:
                if 'request_date' in request_data and request_data['request_date']:
                    self.request_date_input.setDate(QDate.fromString(request_data['request_date'], "yyyy-MM-dd"))
                    print(f"✅ تم تعبئة تاريخ الطلب: {request_data['request_date']}")
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة تاريخ الطلب: {e}")

            try:
                if 'request_number' in request_data and request_data['request_number']:
                    self.request_number_input.setText(request_data['request_number'])
                    print(f"✅ تم تعبئة رقم الطلب: {request_data['request_number']}")
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة رقم الطلب: {e}")

            # تعبئة الفرع
            try:
                branch_name = request_data.get('branch', '')
                branch_id = request_data.get('branch_id', '')
                print(f"🔍 البحث عن الفرع: اسم='{branch_name}', معرف='{branch_id}'")

                branch_found = False
                # البحث بالمعرف أولاً
                if branch_id:
                    for i in range(self.branch_combo.count()):
                        if self.branch_combo.itemData(i) == branch_id:
                            self.branch_combo.setCurrentIndex(i)
                            print(f"✅ تم تعبئة الفرع بالمعرف: {self.branch_combo.currentText()}")
                            branch_found = True
                            break

                # إذا لم يتم العثور بالمعرف، البحث بالاسم
                if not branch_found and branch_name:
                    for i in range(self.branch_combo.count()):
                        if branch_name in self.branch_combo.itemText(i):
                            self.branch_combo.setCurrentIndex(i)
                            print(f"✅ تم تعبئة الفرع بالاسم: {self.branch_combo.currentText()}")
                            branch_found = True
                            break

                if not branch_found:
                    print(f"⚠️ لم يتم العثور على الفرع: {branch_name}")

            except Exception as e:
                print(f"⚠️ خطأ في تعبئة الفرع: {e}")

            # تعبئة الصراف
            try:
                exchanger_name = request_data.get('exchanger', '')
                exchanger_id = request_data.get('exchanger_id', '')
                print(f"🔍 البحث عن الصراف: اسم='{exchanger_name}', معرف='{exchanger_id}'")

                exchanger_found = False
                # البحث بالمعرف أولاً
                if exchanger_id:
                    for i in range(self.exchanger_combo.count()):
                        if self.exchanger_combo.itemData(i) == exchanger_id:
                            self.exchanger_combo.setCurrentIndex(i)
                            print(f"✅ تم تعبئة الصراف بالمعرف: {self.exchanger_combo.currentText()}")
                            exchanger_found = True
                            break

                # إذا لم يتم العثور بالمعرف، البحث بالاسم
                if not exchanger_found and exchanger_name:
                    for i in range(self.exchanger_combo.count()):
                        if exchanger_name in self.exchanger_combo.itemText(i):
                            self.exchanger_combo.setCurrentIndex(i)
                            print(f"✅ تم تعبئة الصراف بالاسم: {self.exchanger_combo.currentText()}")
                            exchanger_found = True
                            break

                if not exchanger_found:
                    print(f"⚠️ لم يتم العثور على الصراف: {exchanger_name}")

            except Exception as e:
                print(f"⚠️ خطأ في تعبئة الصراف: {e}")

            # المبلغ والعملة
            try:
                amount = request_data.get('remittance_amount') or request_data.get('amount', '')
                if amount:
                    self.remittance_amount_input.setText(str(amount))
                    print(f"✅ تم تعبئة المبلغ: {amount}")
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة المبلغ: {e}")

            try:
                if 'currency' in request_data and request_data['currency']:
                    index = self.currency_combo.findText(request_data['currency'])
                    if index >= 0:
                        self.currency_combo.setCurrentIndex(index)
                        print(f"✅ تم تعبئة العملة: {request_data['currency']}")
                    else:
                        print(f"⚠️ لم يتم العثور على العملة: {request_data['currency']}")
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة العملة: {e}")

            try:
                if 'transfer_purpose' in request_data and request_data['transfer_purpose']:
                    self.transfer_purpose_input.setText(request_data['transfer_purpose'])
                    print(f"✅ تم تعبئة غرض التحويل: {request_data['transfer_purpose']}")
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة غرض التحويل: {e}")

            # معلومات المرسل (مع معالجة الأخطاء)
            sender_fields = {
                'sender_name': 'sender_name_input',
                'sender_entity': 'sender_entity_input',
                'sender_phone': 'sender_phone_input',
                'sender_fax': 'sender_fax_input',
                'sender_mobile': 'sender_mobile_input',
                'sender_pobox': 'sender_pobox_input',
                'sender_email': 'sender_email_input',
                'sender_address': 'sender_address_input'
            }

            for data_key, field_name in sender_fields.items():
                try:
                    if data_key in request_data and request_data[data_key] and hasattr(self, field_name):
                        field = getattr(self, field_name)
                        if hasattr(field, 'setText'):
                            field.setText(request_data[data_key])
                            print(f"✅ تم تعبئة {data_key}: {request_data[data_key]}")
                except Exception as e:
                    print(f"⚠️ خطأ في تعبئة {data_key}: {e}")

            # معلومات المرسل
            if 'sender_name' in request_data and request_data['sender_name']:
                self.sender_name_input.setText(request_data['sender_name'])

            if 'sender_entity' in request_data and request_data['sender_entity']:
                self.sender_entity_input.setText(request_data['sender_entity'])

            if 'sender_phone' in request_data and request_data['sender_phone']:
                self.sender_phone_input.setText(request_data['sender_phone'])

            if 'sender_fax' in request_data and request_data['sender_fax']:
                self.sender_fax_input.setText(request_data['sender_fax'])

            if 'sender_mobile' in request_data and request_data['sender_mobile']:
                self.sender_mobile_input.setText(request_data['sender_mobile'])

            if 'sender_pobox' in request_data and request_data['sender_pobox']:
                self.sender_pobox_input.setText(request_data['sender_pobox'])

            if 'sender_email' in request_data and request_data['sender_email']:
                self.sender_email_input.setText(request_data['sender_email'])

            if 'sender_address' in request_data and request_data['sender_address']:
                self.sender_address_input.setText(request_data['sender_address'])

            # معلومات المستقبل (مع معالجة الأخطاء)
            receiver_fields = {
                'receiver_name': 'receiver_name_input',
                'receiver_account': 'receiver_account_input',
                'receiver_bank_branch': 'receiver_bank_branch_input',
                'receiver_swift': 'receiver_swift_input',
                'receiver_country': 'receiver_country_input',
                'receiver_bank_country': 'receiver_bank_country_input',
                'receiver_address': 'receiver_address_input'
            }

            for data_key, field_name in receiver_fields.items():
                try:
                    if data_key in request_data and request_data[data_key] and hasattr(self, field_name):
                        field = getattr(self, field_name)
                        if hasattr(field, 'setText'):
                            field.setText(request_data[data_key])
                            print(f"✅ تم تعبئة {data_key}: {request_data[data_key]}")
                except Exception as e:
                    print(f"⚠️ خطأ في تعبئة {data_key}: {e}")

            # معالجة خاصة لاسم البنك (مفاتيح متعددة)
            try:
                receiver_bank = request_data.get('receiver_bank') or request_data.get('receiver_bank_name', '')
                if receiver_bank and hasattr(self, 'receiver_bank_input'):
                    self.receiver_bank_input.setText(receiver_bank)
                    print(f"✅ تم تعبئة receiver_bank: {receiver_bank}")
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة receiver_bank: {e}")

            # الملاحظات
            if 'notes' in request_data and request_data['notes']:
                self.notes_input.setPlainText(request_data['notes'])

            # الخيارات والإشعارات (مع معالجة الأخطاء)
            try:
                if 'sms_notification' in request_data and hasattr(self, 'sms_notification_check'):
                    self.sms_notification_check.setChecked(bool(request_data['sms_notification']))
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة إشعار SMS: {e}")

            try:
                if 'email_notification' in request_data and hasattr(self, 'email_notification_check'):
                    self.email_notification_check.setChecked(bool(request_data['email_notification']))
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة إشعار البريد الإلكتروني: {e}")

            try:
                if 'auto_create_remittance' in request_data and hasattr(self, 'auto_create_remittance_check'):
                    self.auto_create_remittance_check.setChecked(bool(request_data['auto_create_remittance']))
            except Exception as e:
                print(f"⚠️ خطأ في تعبئة الإنشاء التلقائي: {e}")

            print("✅ تم تعبئة النموذج بجميع البيانات المحفوظة")

        except Exception as e:
            print(f"❌ خطأ في تعبئة النموذج: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"خطأ في تعبئة النموذج:\n{str(e)}")

    def diagnose_saved_data(self, request_data):
        """تشخيص البيانات المحفوظة"""
        print("\n🔍 تشخيص البيانات المحفوظة:")

        # فحص البيانات الأساسية
        basic_fields = ['request_number', 'request_date', 'branch', 'branch_id',
                       'exchanger', 'exchanger_id', 'remittance_amount', 'currency', 'transfer_purpose']
        print("📋 البيانات الأساسية:")
        for field in basic_fields:
            value = request_data.get(field, 'غير موجود')
            print(f"   {field}: {value}")

        # فحص بيانات المرسل
        sender_fields = ['sender_name', 'sender_entity', 'sender_phone', 'sender_fax',
                        'sender_mobile', 'sender_pobox', 'sender_email', 'sender_address']
        print("👤 بيانات المرسل:")
        for field in sender_fields:
            value = request_data.get(field, 'غير موجود')
            print(f"   {field}: {value}")

        # فحص بيانات المستقبل
        receiver_fields = ['receiver_name', 'receiver_account', 'receiver_bank', 'receiver_bank_name',
                          'receiver_bank_branch', 'receiver_swift', 'receiver_country',
                          'receiver_bank_country', 'receiver_address']
        print("🏦 بيانات المستقبل:")
        for field in receiver_fields:
            value = request_data.get(field, 'غير موجود')
            print(f"   {field}: {value}")

        # فحص البيانات المالية
        financial_fields = ['remittance_amount', 'amount', 'currency', 'source_currency', 'target_currency']
        print("💰 البيانات المالية:")
        for field in financial_fields:
            value = request_data.get(field, 'غير موجود')
            print(f"   {field}: {value}")

        # فحص الخيارات
        option_fields = ['sms_notification', 'email_notification', 'auto_create_remittance', 'notes']
        print("⚙️ الخيارات والملاحظات:")
        for field in option_fields:
            value = request_data.get(field, 'غير موجود')
            print(f"   {field}: {value}")

        print("=" * 50)

    def get_table_columns(self, cursor):
        """الحصول على أعمدة الجدول الفعلية"""
        try:
            cursor.execute("PRAGMA table_info(remittance_requests)")
            columns = cursor.fetchall()
            return [column[1] for column in columns]
        except:
            return []

    def insert_request_dynamically(self, cursor, data):
        """إدراج طلب جديد ديناميكياً بناءً على الأعمدة الموجودة"""
        try:
            # الحصول على الأعمدة الفعلية في الجدول
            existing_columns = self.get_table_columns(cursor)
            print(f"🔍 الأعمدة الموجودة في الجدول للإدراج: {existing_columns}")

            # بناء استعلام الإدراج ديناميكياً
            insert_fields = []
            insert_values = []
            placeholders = []

            # قائمة الحقول المطلوبة مع قيمها
            field_mapping = {
                'request_number': data['request_number'],
                'request_date': data['request_date'],
                'branch': data['branch'],
                'branch_id': data.get('branch_id'),
                'exchanger': data['exchanger'],
                'exchanger_id': data.get('exchanger_id'),
                'remittance_amount': data['remittance_amount'],
                'currency': data['currency'],
                'currency_code': data.get('currency_code'),
                'transfer_purpose': data['transfer_purpose'],
                'sender_name': data['sender_name'],
                'sender_entity': data['sender_entity'],
                'sender_phone': data['sender_phone'],
                'sender_fax': data['sender_fax'],
                'sender_mobile': data['sender_mobile'],
                'sender_pobox': data['sender_pobox'],
                'sender_email': data['sender_email'],
                'sender_address': data['sender_address'],
                'receiver_name': data['receiver_name'],
                'receiver_account': data['receiver_account'],
                'receiver_bank': data['receiver_bank'],
                'receiver_bank_branch': data['receiver_bank_branch'],
                'receiver_swift': data['receiver_swift'],
                'receiver_country': data['receiver_country'],
                'receiver_address': data['receiver_address'],
                'notes': data['notes'],
                'sms_notification': data['sms_notification'],
                'email_notification': data['email_notification'],
                'auto_create_remittance': data['auto_create_remittance'],
                'status': data['status'],
                'created_at': data['created_at'],
                # الحقول القديمة للتوافق
                'amount': data['remittance_amount'],
                'source_currency': data['currency'],
                'target_currency': data['currency']
            }

            # إضافة الحقول الموجودة فقط
            for field_name, field_value in field_mapping.items():
                if field_name in existing_columns:
                    insert_fields.append(field_name)
                    insert_values.append(field_value)
                    placeholders.append('?')
                else:
                    print(f"⚠️ تجاهل الحقل غير الموجود: {field_name}")

            # بناء وتنفيذ الاستعلام
            if insert_fields:
                insert_query = f"""
                    INSERT INTO remittance_requests ({', '.join(insert_fields)})
                    VALUES ({', '.join(placeholders)})
                """

                print(f"🔄 تنفيذ استعلام الإدراج: {insert_query}")
                print(f"📊 عدد القيم: {len(insert_values)}")

                cursor.execute(insert_query, insert_values)
                return cursor.lastrowid
            else:
                print("❌ لا توجد حقول صالحة للإدراج")
                return None

        except Exception as e:
            print(f"❌ خطأ في الإدراج الديناميكي: {e}")
            import traceback
            traceback.print_exc()
            return None

    def update_request_in_database(self, request_data, request_id):
        """تحديث طلب في قاعدة البيانات - حل جذري"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التأكد من تحديث الجدول أولاً
            self.create_or_update_table(cursor)

            # الحصول على الأعمدة الفعلية في الجدول
            existing_columns = self.get_table_columns(cursor)
            print(f"🔍 الأعمدة الموجودة في الجدول: {existing_columns}")

            # إضافة updated_at إلى البيانات
            request_data['updated_at'] = datetime.now().isoformat()

            # بناء استعلام التحديث ديناميكياً بناءً على الأعمدة الموجودة
            update_fields = []
            update_values = []

            # قائمة الحقول المطلوبة مع قيمها
            field_mapping = {
                'request_number': request_data.get('request_number', ''),
                'request_date': request_data.get('request_date', ''),
                'exchanger': request_data.get('exchanger', ''),
                'remittance_amount': request_data.get('remittance_amount', ''),
                'currency': request_data.get('currency', ''),
                'transfer_purpose': request_data.get('transfer_purpose', ''),
                'sender_name': request_data.get('sender_name', ''),
                'sender_entity': request_data.get('sender_entity', ''),
                'sender_phone': request_data.get('sender_phone', ''),
                'sender_fax': request_data.get('sender_fax', ''),
                'sender_mobile': request_data.get('sender_mobile', ''),
                'sender_pobox': request_data.get('sender_pobox', ''),
                'sender_email': request_data.get('sender_email', ''),
                'sender_address': request_data.get('sender_address', ''),
                'receiver_name': request_data.get('receiver_name', ''),
                'receiver_account': request_data.get('receiver_account', ''),
                'receiver_bank': request_data.get('receiver_bank', ''),
                'receiver_bank_branch': request_data.get('receiver_bank_branch', ''),
                'receiver_swift': request_data.get('receiver_swift', ''),
                'receiver_country': request_data.get('receiver_country', ''),
                'receiver_bank_country': request_data.get('receiver_bank_country', ''),
                'receiver_address': request_data.get('receiver_address', ''),
                'notes': request_data.get('notes', ''),
                'sms_notification': request_data.get('sms_notification', False),
                'email_notification': request_data.get('email_notification', False),
                'auto_create_remittance': request_data.get('auto_create_remittance', False),
                'status': request_data.get('status', 'معلق'),
                'updated_at': request_data['updated_at']
            }

            # إضافة الحقول الموجودة فقط
            for field_name, field_value in field_mapping.items():
                if field_name in existing_columns:
                    update_fields.append(f"{field_name} = ?")
                    update_values.append(field_value)
                else:
                    print(f"⚠️ تجاهل الحقل غير الموجود: {field_name}")

            # إضافة معرف الطلب في النهاية
            update_values.append(request_id)

            # بناء وتنفيذ الاستعلام
            if update_fields:
                update_query = f"""
                    UPDATE remittance_requests SET
                        {', '.join(update_fields)}
                    WHERE id = ?
                """

                print(f"🔄 تنفيذ الاستعلام: {update_query}")
                print(f"📊 عدد القيم: {len(update_values)}")

                cursor.execute(update_query, update_values)

                if cursor.rowcount > 0:
                    conn.commit()
                    print(f"✅ تم تحديث الطلب {request_id} بنجاح ({cursor.rowcount} صف)")
                    conn.close()
                    return True
                else:
                    print(f"⚠️ لم يتم العثور على الطلب {request_id}")
                    conn.close()
                    return False
            else:
                print("❌ لا توجد حقول صالحة للتحديث")
                conn.close()
                return False

        except Exception as e:
            print(f"❌ خطأ في تحديث الطلب: {e}")
            import traceback
            traceback.print_exc()
            if 'conn' in locals():
                conn.close()
            return False

    def cancel_editing(self):
        """إلغاء وضع التحرير"""
        reply = QMessageBox.question(self, "إلغاء التحرير",
                                   "هل أنت متأكد من إلغاء التحرير؟\nسيتم فقدان التغييرات غير المحفوظة.",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # إعادة تعيين وضع التحرير
            self.editing_request_id = None
            self.cancel_edit_btn.setVisible(False)

            # مسح النموذج
            self.clear_form()

            QMessageBox.information(self, "تم الإلغاء", "تم إلغاء وضع التحرير")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "تصدير البيانات", "ميزة التصدير قيد التطوير...")

    def open_settings(self):
        """فتح الإعدادات"""
        QMessageBox.information(self, "الإعدادات", "نافذة الإعدادات قيد التطوير...")

    def close_application(self):
        """إغلاق التطبيق مع التأكيد"""
        reply = QMessageBox.question(
            self,
            "تأكيد الخروج",
            "هل أنت متأكد من أنك تريد إغلاق نافذة طلب الحوالة؟\n\nسيتم فقدان أي بيانات غير محفوظة.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # التحقق من وجود بيانات غير محفوظة
            if self.has_unsaved_changes():
                save_reply = QMessageBox.question(
                    self,
                    "بيانات غير محفوظة",
                    "يوجد بيانات غير محفوظة. هل تريد حفظها قبل الخروج؟",
                    QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                    QMessageBox.Yes
                )

                if save_reply == QMessageBox.Yes:
                    # محاولة حفظ البيانات
                    if self.save_current_data():
                        self.close()
                    # إذا فشل الحفظ، لا نغلق النافذة
                elif save_reply == QMessageBox.No:
                    self.close()
                # إذا اختار Cancel، لا نفعل شيئاً
            else:
                self.close()

    def has_unsaved_changes(self):
        """التحقق من وجود تغييرات غير محفوظة"""
        # التحقق من وجود نص في الحقول الرئيسية
        if (self.sender_name_input.text().strip() or
            self.receiver_name_input.text().strip() or
            self.remittance_amount_input.text().strip()):
            return True
        return False

    def save_current_data(self):
        """حفظ البيانات الحالية"""
        try:
            if self.editing_request_id:
                # في وضع التحرير - استدعاء دالة التحديث
                request_data = self.collect_new_request_data()
                return self.update_request_in_database(request_data, self.editing_request_id)
            else:
                # طلب جديد
                return self.save_new_request()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ البيانات:\n{str(e)}")
            return False

    def generate_pdf_report(self):
        """إنشاء تقرير PDF لطلب الحوالة"""
        try:
            # التحقق من وجود بيانات
            if not self.validate_new_request_form():
                QMessageBox.warning(self, "بيانات ناقصة",
                                  "يرجى تعبئة البيانات المطلوبة قبل إنشاء PDF")
                return

            # جمع البيانات
            request_data = self.collect_new_request_data()

            # استيراد مولد PDF
            from .remittance_pdf_generator import RemittancePDFGenerator

            # إنشاء مولد PDF
            pdf_generator = RemittancePDFGenerator()

            # تحديد مسار الحفظ
            from PySide6.QtWidgets import QFileDialog
            default_filename = f"طلب_حوالة_{request_data.get('request_number', 'جديد')}.pdf"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف PDF",
                default_filename,
                "PDF Files (*.pdf);;All Files (*)"
            )

            if file_path:
                # إنشاء ملف PDF
                output_path = pdf_generator.generate_pdf(request_data, file_path)

                # رسالة نجاح
                reply = QMessageBox.question(
                    self,
                    "تم إنشاء PDF",
                    f"تم إنشاء ملف PDF بنجاح!\n\nالمسار: {output_path}\n\nهل تريد فتح الملف؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    # فتح الملف
                    import os
                    import subprocess
                    import platform

                    try:
                        if platform.system() == 'Windows':
                            os.startfile(output_path)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.run(['open', output_path])
                        else:  # Linux
                            subprocess.run(['xdg-open', output_path])
                    except Exception as e:
                        QMessageBox.information(self, "ملف PDF",
                                              f"تم إنشاء الملف بنجاح في:\n{output_path}")

        except ImportError:
            QMessageBox.critical(self, "خطأ",
                               "مكتبات PDF غير متوفرة. يرجى تثبيت المكتبات المطلوبة:\n"
                               "pip install reportlab arabic-reshaper python-bidi")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء ملف PDF:\n{str(e)}")

    # دوال دفتر العناوين
    def load_address_book_data(self):
        """تحميل بيانات دفتر العناوين"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إنشاء جدول دفتر العناوين إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS address_book (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    receiver_name TEXT NOT NULL,
                    receiver_account TEXT,
                    receiver_bank TEXT,
                    receiver_bank_branch TEXT,
                    receiver_swift TEXT,
                    receiver_country TEXT,
                    receiver_bank_country TEXT,
                    receiver_address TEXT,
                    receiver_phone TEXT,
                    receiver_email TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جلب البيانات
            cursor.execute("""
                SELECT id, receiver_name, receiver_account, receiver_bank,
                       receiver_bank_branch, receiver_swift, receiver_country,
                       receiver_bank_country, receiver_address
                FROM address_book
                WHERE is_active = 1
                ORDER BY receiver_name
            """)

            addresses = cursor.fetchall()

            # تحديث الجدول
            self.address_book_table.setRowCount(len(addresses))

            for row, address in enumerate(addresses):
                for col, value in enumerate(address[1:9]):  # تجاهل ID
                    item = QTableWidgetItem(str(value) if value else "")
                    self.address_book_table.setItem(row, col, item)

                # إضافة أزرار العمليات
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(5, 5, 5, 5)

                # زر التحديد
                select_btn = QPushButton("✅ تحديد")
                select_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #3498db;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 3px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #2980b9;
                    }
                """)
                select_btn.clicked.connect(lambda checked, addr_id=address[0]: self.select_address_book_entry(addr_id))
                actions_layout.addWidget(select_btn)

                # زر التعديل
                edit_btn = QPushButton("✏️ تعديل")
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #f39c12;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 3px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #e67e22;
                    }
                """)
                edit_btn.clicked.connect(lambda checked, addr_id=address[0]: self.edit_address_book_entry(addr_id))
                actions_layout.addWidget(edit_btn)

                # زر الحذف
                delete_btn = QPushButton("🗑️ حذف")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 3px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, addr_id=address[0]: self.delete_address_book_entry(addr_id))
                actions_layout.addWidget(delete_btn)

                self.address_book_table.setCellWidget(row, 8, actions_widget)

            conn.close()

            # تحديث عداد العناوين
            if hasattr(self, 'address_count_label'):
                self.address_count_label.setText(f"📊 العناوين: {len(addresses)}")

            print(f"✅ تم تحميل {len(addresses)} عنوان من دفتر العناوين")

        except Exception as e:
            print(f"❌ خطأ في تحميل دفتر العناوين: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل دفتر العناوين:\n{str(e)}")

    def save_address_book_entry(self):
        """حفظ أو تحديث عنوان في دفتر العناوين"""
        try:
            # التحقق من البيانات المطلوبة
            if not self.ab_receiver_name_input.text().strip():
                QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم المستقبل")
                return

            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # التحقق من وضع التعديل
            if hasattr(self, 'current_editing_address_id') and self.current_editing_address_id:
                # تحديث العنوان الموجود
                cursor.execute("""
                    UPDATE address_book SET
                        receiver_name = ?, receiver_account = ?, receiver_bank = ?, receiver_bank_branch = ?,
                        receiver_swift = ?, receiver_country = ?, receiver_bank_country = ?, receiver_address = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (
                    self.ab_receiver_name_input.text().strip(),
                    self.ab_receiver_account_input.text().strip(),
                    self.ab_receiver_bank_input.text().strip(),
                    self.ab_receiver_bank_branch_input.text().strip(),
                    self.ab_receiver_swift_input.text().strip(),
                    self.ab_receiver_country_input.text().strip(),
                    self.ab_receiver_bank_country_input.text().strip(),
                    self.ab_receiver_address_input.text().strip(),
                    self.current_editing_address_id
                ))

                message = "تم تحديث العنوان بنجاح"

            else:
                # إدراج عنوان جديد
                cursor.execute("""
                    INSERT INTO address_book (
                        receiver_name, receiver_account, receiver_bank, receiver_bank_branch,
                        receiver_swift, receiver_country, receiver_bank_country, receiver_address
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    self.ab_receiver_name_input.text().strip(),
                    self.ab_receiver_account_input.text().strip(),
                    self.ab_receiver_bank_input.text().strip(),
                    self.ab_receiver_bank_branch_input.text().strip(),
                    self.ab_receiver_swift_input.text().strip(),
                    self.ab_receiver_country_input.text().strip(),
                    self.ab_receiver_bank_country_input.text().strip(),
                    self.ab_receiver_address_input.text().strip()
                ))

                message = "تم حفظ العنوان الجديد بنجاح"

            conn.commit()
            conn.close()

            QMessageBox.information(self, "تم الحفظ", message)

            # إعادة تعيين النموذج وإعادة تحميل البيانات
            self.cancel_address_edit()
            self.load_address_book_data()

            # تحديث قائمة البحث التلقائي
            self.update_receiver_names_list()

        except Exception as e:
            print(f"❌ خطأ في حفظ العنوان: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ العنوان:\n{str(e)}")

    def clear_address_book_form(self):
        """مسح نموذج دفتر العناوين"""
        self.ab_receiver_name_input.clear()
        self.ab_receiver_account_input.clear()
        self.ab_receiver_bank_input.clear()
        self.ab_receiver_bank_branch_input.clear()
        self.ab_receiver_swift_input.clear()
        self.ab_receiver_country_input.clear()
        self.ab_receiver_bank_country_input.clear()
        self.ab_receiver_address_input.clear()

    def select_address_book_entry(self, address_id):
        """تحديد عنوان من دفتر العناوين وملء النموذج"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT receiver_name, receiver_account, receiver_bank, receiver_bank_branch,
                       receiver_swift, receiver_country, receiver_bank_country, receiver_address
                FROM address_book WHERE id = ?
            """, (address_id,))

            address = cursor.fetchone()
            conn.close()

            if address:
                # ملء حقول معلومات المستقبل في تبويب طلب حوالة جديد
                self.receiver_name_input.setText(address[0] or "")
                self.receiver_account_input.setText(address[1] or "")
                self.receiver_bank_input.setText(address[2] or "")
                self.receiver_bank_branch_input.setText(address[3] or "")
                self.receiver_swift_input.setText(address[4] or "")
                self.receiver_country_input.setText(address[5] or "")
                self.receiver_bank_country_input.setText(address[6] or "")
                self.receiver_address_input.setText(address[7] or "")

                # الانتقال إلى تبويب طلب حوالة جديد
                self.tab_widget.setCurrentIndex(1)

                QMessageBox.information(self, "تم التحديد", "تم تحديد العنوان وملء البيانات في نموذج طلب الحوالة")

        except Exception as e:
            print(f"❌ خطأ في تحديد العنوان: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحديد العنوان:\n{str(e)}")

    def edit_address_book_entry(self, address_id):
        """تعديل عنوان في دفتر العناوين"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT receiver_name, receiver_account, receiver_bank, receiver_bank_branch,
                       receiver_swift, receiver_country, receiver_bank_country, receiver_address
                FROM address_book WHERE id = ?
            """, (address_id,))

            address = cursor.fetchone()
            conn.close()

            if address:
                # ملء نموذج التعديل
                self.ab_receiver_name_input.setText(address[0] or "")
                self.ab_receiver_account_input.setText(address[1] or "")
                self.ab_receiver_bank_input.setText(address[2] or "")
                self.ab_receiver_bank_branch_input.setText(address[3] or "")
                self.ab_receiver_swift_input.setText(address[4] or "")
                self.ab_receiver_country_input.setText(address[5] or "")
                self.ab_receiver_bank_country_input.setText(address[6] or "")
                self.ab_receiver_address_input.setText(address[7] or "")

                # تحديث حالة النموذج للتعديل
                self.form_status_label.setText(f"✏️ تعديل العنوان: {address[0]}")
                self.save_address_btn.setText("💾 تحديث العنوان")
                self.cancel_edit_btn.setVisible(True)

                # حفظ معرف العنوان للتحديث
                self.current_editing_address_id = address_id

        except Exception as e:
            print(f"❌ خطأ في تحميل العنوان للتعديل: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل العنوان للتعديل:\n{str(e)}")

    def delete_address_book_entry(self, address_id):
        """حذف عنوان من دفتر العناوين"""
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   "هل أنت متأكد من حذف هذا العنوان؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                db_path = Path("data/proshipment.db")
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()

                cursor.execute("UPDATE address_book SET is_active = 0 WHERE id = ?", (address_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "تم الحذف", "تم حذف العنوان من دفتر العناوين")
                self.load_address_book_data()

            except Exception as e:
                print(f"❌ خطأ في حذف العنوان: {e}")
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف العنوان:\n{str(e)}")

    def search_address_book(self):
        """البحث في دفتر العناوين"""
        search_text = self.address_search_input.text().strip().lower()

        for row in range(self.address_book_table.rowCount()):
            show_row = False

            # البحث في جميع الأعمدة
            for col in range(8):  # تجاهل عمود العمليات
                item = self.address_book_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.address_book_table.setRowHidden(row, not show_row)

    def open_address_book_tab(self):
        """فتح تبويب دفتر العناوين"""
        self.tab_widget.setCurrentIndex(3)  # تبويب دفتر العناوين

    def setup_receiver_name_autocomplete(self):
        """إعداد البحث التلقائي لحقل اسم المستقبل"""
        try:
            # إنشاء QCompleter
            self.receiver_name_completer = QCompleter()
            self.receiver_name_completer.setCaseSensitivity(Qt.CaseInsensitive)
            self.receiver_name_completer.setFilterMode(Qt.MatchContains)

            # ربط الـ completer بحقل الاسم
            self.receiver_name_input.setCompleter(self.receiver_name_completer)

            # ربط إشارة التحديد بدالة ملء البيانات
            self.receiver_name_completer.activated.connect(self.on_receiver_name_selected)

            # تحديث قائمة الأسماء
            self.update_receiver_names_list()

        except Exception as e:
            print(f"❌ خطأ في إعداد البحث التلقائي: {e}")

    def update_receiver_names_list(self):
        """تحديث قائمة أسماء المستقبلين للبحث التلقائي"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # جلب أسماء المستقبلين من دفتر العناوين
            cursor.execute("""
                SELECT DISTINCT receiver_name
                FROM address_book
                WHERE is_active = 1 AND receiver_name IS NOT NULL AND receiver_name != ''
                ORDER BY receiver_name
            """)

            names = [row[0] for row in cursor.fetchall()]
            conn.close()

            # تحديث نموذج البيانات للـ completer
            if hasattr(self, 'receiver_name_completer'):
                model = QStringListModel(names)
                self.receiver_name_completer.setModel(model)

        except Exception as e:
            print(f"❌ خطأ في تحديث قائمة الأسماء: {e}")

    def on_receiver_name_selected(self, name):
        """عند تحديد اسم من قائمة البحث التلقائي"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # البحث عن بيانات المستقبل
            cursor.execute("""
                SELECT receiver_account, receiver_bank, receiver_bank_branch,
                       receiver_swift, receiver_country, receiver_bank_country, receiver_address
                FROM address_book
                WHERE receiver_name = ? AND is_active = 1
                LIMIT 1
            """, (name,))

            result = cursor.fetchone()
            conn.close()

            if result:
                # ملء الحقول تلقائياً
                self.receiver_account_input.setText(result[0] or "")
                self.receiver_bank_input.setText(result[1] or "")
                self.receiver_bank_branch_input.setText(result[2] or "")
                self.receiver_swift_input.setText(result[3] or "")
                self.receiver_country_input.setText(result[4] or "")
                self.receiver_bank_country_input.setText(result[5] or "")
                self.receiver_address_input.setText(result[6] or "")

                print(f"✅ تم ملء بيانات المستقبل: {name}")

        except Exception as e:
            print(f"❌ خطأ في ملء بيانات المستقبل: {e}")

    def check_and_save_to_address_book(self, request_data):
        """التحقق من حفظ معلومات المستقبل في دفتر العناوين"""
        try:
            receiver_name = request_data.get('receiver_name', '').strip()

            # التحقق من وجود بيانات المستقبل
            if not receiver_name:
                return

            # التحقق من وجود المستقبل في دفتر العناوين
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT COUNT(*) FROM address_book
                WHERE receiver_name = ? AND is_active = 1
            """, (receiver_name,))

            exists = cursor.fetchone()[0] > 0
            conn.close()

            # إذا لم يكن موجوداً، اسأل المستخدم
            if not exists:
                reply = QMessageBox.question(
                    self,
                    "حفظ في دفتر العناوين",
                    f"هل تريد إضافة معلومات المستقبل '{receiver_name}' إلى دفتر العناوين؟\n\n"
                    "سيساعدك هذا في ملء البيانات تلقائياً في المرات القادمة.",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    self.save_receiver_to_address_book(request_data)

        except Exception as e:
            print(f"❌ خطأ في التحقق من دفتر العناوين: {e}")

    def save_receiver_to_address_book(self, request_data):
        """حفظ معلومات المستقبل في دفتر العناوين"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # إدراج المستقبل في دفتر العناوين
            cursor.execute("""
                INSERT INTO address_book (
                    receiver_name, receiver_account, receiver_bank, receiver_bank_branch,
                    receiver_swift, receiver_country, receiver_bank_country, receiver_address
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                request_data.get('receiver_name', ''),
                request_data.get('receiver_account', ''),
                request_data.get('receiver_bank', ''),
                request_data.get('receiver_bank_branch', ''),
                request_data.get('receiver_swift', ''),
                request_data.get('receiver_country', ''),
                request_data.get('receiver_bank_country', ''),
                request_data.get('receiver_address', '')
            ))

            conn.commit()
            conn.close()

            # تحديث قائمة البحث التلقائي
            self.update_receiver_names_list()

            QMessageBox.information(
                self,
                "تم الحفظ",
                f"تم حفظ معلومات المستقبل '{request_data.get('receiver_name', '')}' في دفتر العناوين بنجاح!"
            )

        except Exception as e:
            print(f"❌ خطأ في حفظ المستقبل في دفتر العناوين: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في حفظ المستقبل في دفتر العناوين:\n{str(e)}")

    # ==================== نظام الحالات المتقدم ====================

    def collect_form_fields(self):
        """جمع جميع حقول النموذج للتحكم في التفعيل"""
        try:
            self.form_fields = []

            # البيانات الأساسية
            basic_fields = [
                'request_date_input', 'branch_combo', 'exchanger_combo',
                'currency_combo', 'remittance_amount_input', 'transfer_purpose_input'
            ]

            # معلومات المرسل
            sender_fields = [
                'sender_name_input', 'sender_id_input', 'sender_phone_input',
                'sender_address_input', 'sender_nationality_input'
            ]

            # معلومات المستقبل
            receiver_fields = [
                'receiver_name_input', 'receiver_account_input', 'receiver_bank_input',
                'receiver_bank_branch_input', 'receiver_swift_input', 'receiver_country_input',
                'receiver_bank_country_input', 'receiver_address_input'
            ]

            # الملاحظات والخيارات
            notes_fields = [
                'notes_input', 'auto_create_remittance_check'
            ]

            # جمع جميع الحقول
            all_field_names = basic_fields + sender_fields + receiver_fields + notes_fields

            for field_name in all_field_names:
                if hasattr(self, field_name):
                    field = getattr(self, field_name)
                    self.form_fields.append(field)

            print(f"✅ تم جمع {len(self.form_fields)} حقل للتحكم في التفعيل")

        except Exception as e:
            print(f"❌ خطأ في جمع حقول النموذج: {e}")

    def set_window_mode(self, mode):
        """تعيين حالة النافذة (DISABLED, NEW, EDIT)"""
        try:
            self.window_mode = mode

            if mode == "DISABLED":
                self.set_disabled_mode()
            elif mode == "NEW":
                self.set_new_mode()
            elif mode == "EDIT":
                self.set_edit_mode()

            print(f"🔄 تم تغيير حالة النافذة إلى: {mode}")

        except Exception as e:
            print(f"❌ خطأ في تعيين حالة النافذة: {e}")

    def set_disabled_mode(self):
        """تعطيل جميع الحقول (الحالة الافتراضية)"""
        try:
            # تعطيل جميع حقول النموذج
            for field in self.form_fields:
                if field:
                    field.setEnabled(False)

            # تحديث مؤشر الحالة
            if hasattr(self, 'form_status_label'):
                self.form_status_label.setText("🔒 النموذج معطل - اختر عملية")
                self.form_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #e74c3c;
                        padding: 10px 20px;
                        background-color: #fadbd8;
                        border: 2px solid #e74c3c;
                        border-radius: 8px;
                        margin-right: 20px;
                    }
                """)

            # تحديث حالة الأزرار
            if hasattr(self, 'add_new_btn'):
                self.add_new_btn.setEnabled(True)
            if hasattr(self, 'edit_btn'):
                self.edit_btn.setEnabled(True)
            if hasattr(self, 'save_request_btn'):
                self.save_request_btn.setEnabled(False)
            if hasattr(self, 'cancel_btn'):
                self.cancel_btn.setEnabled(False)

            # مسح النموذج
            self.clear_form()

            print("🔒 تم تعطيل النموذج")

        except Exception as e:
            print(f"❌ خطأ في تعطيل النموذج: {e}")

    def set_new_mode(self):
        """تفعيل النموذج لإدخال طلب جديد"""
        try:
            # تفعيل جميع حقول النموذج
            for field in self.form_fields:
                if field:
                    field.setEnabled(True)

            # تحديث مؤشر الحالة
            if hasattr(self, 'form_status_label'):
                self.form_status_label.setText("➕ وضع إضافة طلب جديد")
                self.form_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #27ae60;
                        padding: 10px 20px;
                        background-color: #d5f4e6;
                        border: 2px solid #27ae60;
                        border-radius: 8px;
                        margin-right: 20px;
                    }
                """)

            # تحديث حالة الأزرار
            if hasattr(self, 'add_new_btn'):
                self.add_new_btn.setEnabled(False)
            if hasattr(self, 'edit_btn'):
                self.edit_btn.setEnabled(False)
            if hasattr(self, 'save_request_btn'):
                self.save_request_btn.setEnabled(True)
                self.save_request_btn.setText("💾 حفظ طلب جديد")
            if hasattr(self, 'cancel_btn'):
                self.cancel_btn.setEnabled(True)

            # مسح النموذج وإعداده للإدخال الجديد
            self.clear_form()
            self.current_editing_request_id = None

            print("➕ تم تفعيل وضع الإضافة")

        except Exception as e:
            print(f"❌ خطأ في تفعيل وضع الإضافة: {e}")

    def set_edit_mode(self):
        """تفعيل النموذج لتعديل طلب موجود"""
        try:
            # التحقق من وجود طلب محدد للتعديل
            if not self.selected_request_id:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد طلب من القائمة للتعديل")
                return

            # تفعيل جميع حقول النموذج
            for field in self.form_fields:
                if field:
                    field.setEnabled(True)

            # تحديث مؤشر الحالة
            if hasattr(self, 'form_status_label'):
                self.form_status_label.setText("✏️ وضع تعديل طلب موجود")
                self.form_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 16px;
                        font-weight: bold;
                        color: #f39c12;
                        padding: 10px 20px;
                        background-color: #fef9e7;
                        border: 2px solid #f39c12;
                        border-radius: 8px;
                        margin-right: 20px;
                    }
                """)

            # تحديث حالة الأزرار
            if hasattr(self, 'add_new_btn'):
                self.add_new_btn.setEnabled(False)
            if hasattr(self, 'edit_btn'):
                self.edit_btn.setEnabled(False)
            if hasattr(self, 'save_request_btn'):
                self.save_request_btn.setEnabled(True)
                self.save_request_btn.setText("💾 تحديث الطلب")
            if hasattr(self, 'cancel_btn'):
                self.cancel_btn.setEnabled(True)

            # تحميل بيانات الطلب المحدد
            self.load_request_for_editing(self.selected_request_id)
            self.current_editing_request_id = self.selected_request_id

            print(f"✏️ تم تفعيل وضع التعديل للطلب: {self.selected_request_id}")

        except Exception as e:
            print(f"❌ خطأ في تفعيل وضع التعديل: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تفعيل وضع التعديل:\n{str(e)}")

    def enable_new_mode(self):
        """تفعيل وضع إضافة طلب جديد"""
        self.set_window_mode("NEW")

    def enable_edit_mode(self):
        """تفعيل وضع تعديل طلب"""
        self.set_window_mode("EDIT")

    def cancel_operation(self):
        """إلغاء العملية الحالية والعودة للوضع المعطل"""
        try:
            reply = QMessageBox.question(
                self, "تأكيد الإلغاء",
                "هل أنت متأكد من إلغاء العملية الحالية؟\nسيتم فقدان جميع البيانات غير المحفوظة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.set_window_mode("DISABLED")
                print("❌ تم إلغاء العملية")

        except Exception as e:
            print(f"❌ خطأ في إلغاء العملية: {e}")

    def load_request_for_editing(self, request_id):
        """تحميل بيانات طلب للتعديل"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            cursor.execute("""
                SELECT * FROM remittance_requests WHERE id = ?
            """, (request_id,))

            request = cursor.fetchone()
            conn.close()

            if request:
                # ملء النموذج ببيانات الطلب
                self.fill_form_with_request_data(request)
                print(f"✅ تم تحميل بيانات الطلب {request_id} للتعديل")
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الطلب المحدد")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الطلب: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل بيانات الطلب:\n{str(e)}")

    def fill_form_with_request_data(self, request_data):
        """ملء النموذج ببيانات طلب موجود"""
        try:
            # ملء البيانات الأساسية
            if hasattr(self, 'request_date_input') and request_data[2]:  # request_date
                from PySide6.QtCore import QDate
                date = QDate.fromString(request_data[2], "yyyy-MM-dd")
                self.request_date_input.setDate(date)

            # ملء باقي الحقول
            field_mappings = {
                'remittance_amount_input': 4,  # remittance_amount
                'transfer_purpose_input': 7,   # transfer_purpose
                'sender_name_input': 8,        # sender_name
                'sender_id_input': 9,          # sender_id
                'sender_phone_input': 10,      # sender_phone
                'sender_address_input': 11,    # sender_address
                'sender_nationality_input': 12, # sender_nationality
                'receiver_name_input': 13,     # receiver_name
                'receiver_account_input': 14,  # receiver_account
                'receiver_bank_input': 15,     # receiver_bank
                'receiver_bank_branch_input': 16, # receiver_bank_branch
                'receiver_swift_input': 17,    # receiver_swift
                'receiver_country_input': 18,  # receiver_country
                'receiver_bank_country_input': 19, # receiver_bank_country
                'receiver_address_input': 20,  # receiver_address
                'notes_input': 21,             # notes
            }

            for field_name, index in field_mappings.items():
                if hasattr(self, field_name) and len(request_data) > index and request_data[index]:
                    field = getattr(self, field_name)
                    if hasattr(field, 'setText'):
                        field.setText(str(request_data[index]))
                    elif hasattr(field, 'setChecked'):
                        field.setChecked(bool(request_data[index]))

            print("✅ تم ملء النموذج ببيانات الطلب")

        except Exception as e:
            print(f"❌ خطأ في ملء النموذج: {e}")
            QMessageBox.critical(self, "خطأ", f"خطأ في ملء النموذج:\n{str(e)}")
