#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت متطلبات نظام إدارة الحوالات
ProShipment Management System Requirements Installer

هذا السكريبت يقوم بتثبيت جميع المتطلبات المطلوبة للمشروع
"""

import sys
import subprocess
import os
from pathlib import Path

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f"📦 {title}")
    print("=" * 60)

def print_step(step, description):
    """طباعة خطوة مع تنسيق"""
    print(f"\n📋 الخطوة {step}: {description}")
    print("-" * 40)

def check_python_version():
    """فحص إصدار Python"""
    print_step(1, "فحص إصدار Python")
    
    python_version = sys.version_info
    print(f"إصدار Python الحالي: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print("يرجى تحديث Python من: https://www.python.org/downloads/")
        return False
    
    print("✅ إصدار Python مناسب")
    return True

def check_pip():
    """فحص وجود pip"""
    print_step(2, "فحص أداة pip")
    
    try:
        import pip
        print(f"✅ pip متوفر: {pip.__version__}")
        return True
    except ImportError:
        print("❌ pip غير متوفر")
        print("يرجى تثبيت pip أولاً")
        return False

def upgrade_pip():
    """تحديث pip"""
    print_step(3, "تحديث pip")
    
    try:
        cmd = [sys.executable, "-m", "pip", "install", "--upgrade", "pip"]
        print(f"تنفيذ: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تحديث pip بنجاح")
            return True
        else:
            print(f"⚠️ تحذير: لم يتم تحديث pip")
            print(f"الخطأ: {result.stderr}")
            return True  # نستمر حتى لو فشل التحديث
    except Exception as e:
        print(f"⚠️ تحذير: خطأ في تحديث pip: {e}")
        return True  # نستمر حتى لو فشل التحديث

def install_requirements():
    """تثبيت المتطلبات من ملف requirements.txt"""
    print_step(4, "تثبيت المتطلبات")
    
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        cmd = [
            sys.executable, "-m", "pip", "install", 
            "-r", str(requirements_file),
            "--upgrade"
        ]
        
        print(f"تنفيذ: {' '.join(cmd)}")
        print("جاري تثبيت المتطلبات... (قد يستغرق بضع دقائق)")
        
        # تشغيل الأمر مع عرض المخرجات
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            encoding='utf-8'
        )
        
        # عرض المخرجات في الوقت الفعلي
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        # فحص نتيجة العملية
        return_code = process.poll()
        
        if return_code == 0:
            print("✅ تم تثبيت جميع المتطلبات بنجاح")
            return True
        else:
            print(f"❌ فشل في تثبيت المتطلبات (رمز الخطأ: {return_code})")
            return False
            
    except Exception as e:
        print(f"❌ خطأ أثناء تثبيت المتطلبات: {e}")
        return False

def verify_installation():
    """التحقق من تثبيت المتطلبات الأساسية"""
    print_step(5, "التحقق من التثبيت")
    
    essential_packages = [
        ('PySide6', 'PySide6'),
        ('PyInstaller', 'PyInstaller'),
        ('reportlab', 'reportlab'),
        ('Pillow', 'PIL'),
        ('cryptography', 'cryptography'),
        ('openpyxl', 'openpyxl'),
        ('requests', 'requests'),
        ('psutil', 'psutil'),
    ]
    
    failed_packages = []
    
    for package_name, import_name in essential_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}: مثبت بنجاح")
        except ImportError:
            print(f"❌ {package_name}: فشل في التثبيت")
            failed_packages.append(package_name)
    
    if failed_packages:
        print(f"\n❌ فشل في تثبيت: {', '.join(failed_packages)}")
        print("يرجى تثبيت هذه الحزم يدوياً:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    else:
        print("\n✅ تم تثبيت جميع المتطلبات الأساسية بنجاح")
        return True

def create_virtual_environment():
    """إنشاء بيئة افتراضية (اختياري)"""
    print_step("اختياري", "إنشاء بيئة افتراضية")
    
    response = input("هل تريد إنشاء بيئة افتراضية؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم']:
        try:
            venv_path = Path("venv")
            
            if venv_path.exists():
                print("البيئة الافتراضية موجودة بالفعل")
                return True
            
            cmd = [sys.executable, "-m", "venv", "venv"]
            print(f"تنفيذ: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ تم إنشاء البيئة الافتراضية")
                print("لتفعيل البيئة الافتراضية:")
                print("  Windows: venv\\Scripts\\activate")
                print("  Linux/Mac: source venv/bin/activate")
                return True
            else:
                print(f"❌ فشل في إنشاء البيئة الافتراضية: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء البيئة الافتراضية: {e}")
            return False
    else:
        print("تم تخطي إنشاء البيئة الافتراضية")
        return True

def main():
    """الدالة الرئيسية"""
    print_header("تثبيت متطلبات نظام إدارة الحوالات")
    print("ProShipment Management System Requirements Installation")
    
    try:
        # فحص إصدار Python
        if not check_python_version():
            return 1
        
        # فحص pip
        if not check_pip():
            return 1
        
        # تحديث pip
        upgrade_pip()
        
        # إنشاء بيئة افتراضية (اختياري)
        create_virtual_environment()
        
        # تثبيت المتطلبات
        if not install_requirements():
            return 1
        
        # التحقق من التثبيت
        if not verify_installation():
            return 1
        
        print_header("اكتمل التثبيت بنجاح")
        print("🎉 تم تثبيت جميع المتطلبات بنجاح!")
        print("\nالخطوات التالية:")
        print("1. يمكنك الآن تشغيل التطبيق: python main.py")
        print("2. لبناء ملف تنفيذي: python build.py")
        print("3. للحصول على المساعدة: راجع ملف README.md")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف العملية بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🔚 انتهت عملية التثبيت برمز الخروج: {exit_code}")
    
    if exit_code == 0:
        input("\nاضغط Enter للخروج...")
    
    sys.exit(exit_code)
