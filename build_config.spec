# -*- mode: python ; coding: utf-8 -*-
"""
ملف تكوين PyInstaller لنظام إدارة الحوالات
ProShipment Management System Build Configuration
"""

import sys
import os
from pathlib import Path

# مسار المشروع
project_root = Path.cwd()
src_path = project_root / "src"
release_path = project_root / "release"

# تحديد الملفات والمجلدات المطلوبة
block_cipher = None

# الملفات الإضافية المطلوبة
added_files = [
    # ملفات البيانات
    ('data', 'data'),
    
    # ملفات الأصول (إذا كانت موجودة)
    ('assets', 'assets') if (project_root / 'assets').exists() else None,
    
    # ملفات التكوين
    ('config', 'config') if (project_root / 'config').exists() else None,
    
    # ملفات الموارد
    ('resources', 'resources') if (project_root / 'resources').exists() else None,
]

# إزالة العناصر الفارغة
added_files = [item for item in added_files if item is not None]

# المكتبات المخفية المطلوبة
hidden_imports = [
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'PySide6.QtPrintSupport',
    'sqlite3',
    'pathlib',
    'datetime',
    'json',
    'shutil',
    'os',
    'sys',
    'traceback',
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.lib',
    'reportlab.lib.pagesizes',
    'reportlab.lib.styles',
    'reportlab.platypus',
    'PIL',
    'PIL.Image',
    'cryptography',
    'openpyxl',
    'requests',
    'psutil',
]

# تحليل الملف الرئيسي
a = Analysis(
    ['main.py'],
    pathex=[str(project_root), str(src_path)],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'jupyter',
        'IPython',
        'notebook',
        'pytest',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# معالجة الملفات المكررة
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# إنشاء الملف التنفيذي
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ProShipment',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # إخفاء نافذة وحدة التحكم
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icons/app_icon.ico' if (project_root / 'assets/icons/app_icon.ico').exists() else None,
    version_file='version_info.txt' if (project_root / 'version_info.txt').exists() else None,
)

# جمع جميع الملفات
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='ProShipment',
)

# إنشاء ملف تثبيت واحد (اختياري)
# exe_onefile = EXE(
#     pyz,
#     a.scripts,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     [],
#     name='ProShipment_Portable',
#     debug=False,
#     bootloader_ignore_signals=False,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     runtime_tmpdir=None,
#     console=False,
#     disable_windowed_traceback=False,
#     target_arch=None,
#     codesign_identity=None,
#     entitlements_file=None,
#     icon='assets/icons/app_icon.ico' if (project_root / 'assets/icons/app_icon.ico').exists() else None,
# )
