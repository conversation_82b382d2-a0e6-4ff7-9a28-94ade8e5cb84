# -*- coding: utf-8 -*-
"""
نافذة إدارة المرفقات - نظام إدارة الحوالات
Attachments Manager Window
"""

from PySide6.QtWidgets import (QDialog, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QLineEdit, QTextEdit,
                               QFrame, QGroupBox, QGridLayout, QMessageBox,
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QFileDialog, QProgressBar, QSplitter,
                               QScrollArea, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, Signal, QThread, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon

import sqlite3
import os
import shutil
from pathlib import Path
from datetime import datetime


class AttachmentsManagerWindow(QDialog):
    """نافذة إدارة المرفقات والمستندات"""
    
    def __init__(self, parent=None, entity_type="", entity_id=None, entity_title=""):
        super().__init__(parent)
        self.entity_type = entity_type  # نوع الكيان (remittance_request, etc.)
        self.entity_id = entity_id      # معرف الكيان
        self.entity_title = entity_title # عنوان الكيان
        
        self.attachments_data = []      # قائمة المرفقات
        
        self.setup_ui()
        self.setup_database()
        self.load_attachments()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"إضافة واستعراض المرفقات - {self.entity_title}")
        self.setModal(True)
        self.resize(800, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel(f"📎 إضافة واستعراض المرفقات والمستندات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # معلومات الكيان
        info_label = QLabel(f"الكيان: {self.entity_title}")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                padding: 5px;
            }
        """)
        main_layout.addWidget(info_label)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        # زر إضافة مرفق
        self.add_attachment_btn = QPushButton("📎 إضافة مرفق")
        self.add_attachment_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
        """)
        self.add_attachment_btn.clicked.connect(self.add_attachment)
        buttons_layout.addWidget(self.add_attachment_btn)
        
        # زر حذف مرفق
        self.delete_attachment_btn = QPushButton("🗑️ حذف مرفق")
        self.delete_attachment_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        self.delete_attachment_btn.clicked.connect(self.delete_attachment)
        self.delete_attachment_btn.setEnabled(False)
        buttons_layout.addWidget(self.delete_attachment_btn)
        
        buttons_layout.addStretch()
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # جدول المرفقات
        self.attachments_table = QTableWidget()
        self.attachments_table.setColumnCount(5)
        self.attachments_table.setHorizontalHeaderLabels([
            "اسم الملف", "النوع", "الحجم", "تاريخ الإضافة", "الإجراءات"
        ])
        
        # تنسيق الجدول
        self.attachments_table.horizontalHeader().setStretchLastSection(True)
        self.attachments_table.setAlternatingRowColors(True)
        self.attachments_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # زيادة ارتفاع الصفوف لاحتواء أزرار الإجراءات بشكل متكامل
        self.attachments_table.verticalHeader().setDefaultSectionSize(60)

        # تحديد عرض الأعمدة
        self.setup_attachments_table_columns()

        self.attachments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 45px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                min-height: 40px;
            }
        """)
        
        main_layout.addWidget(self.attachments_table)
        
        # شريط الحالة
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 4px;
                color: #2c3e50;
            }
        """)
        main_layout.addWidget(self.status_label)

    def setup_attachments_table_columns(self):
        """تحديد عرض أعمدة جدول المرفقات لاحتواء أفضل للأزرار"""
        try:
            header = self.attachments_table.horizontalHeader()

            # تحديد عرض كل عمود
            column_widths = {
                0: 200,  # اسم الملف
                1: 80,   # النوع
                2: 100,  # الحجم
                3: 150,  # تاريخ الإضافة
                4: 120   # الإجراءات
            }

            # تطبيق العرض على كل عمود
            for column, width in column_widths.items():
                self.attachments_table.setColumnWidth(column, width)

            # جعل عمود اسم الملف قابل للتمدد
            header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

            # جعل عمود الإجراءات ثابت العرض
            header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)

            print("✅ تم تحديد عرض أعمدة جدول المرفقات")

        except Exception as e:
            print(f"❌ خطأ في تحديد عرض الأعمدة: {e}")

    def setup_database(self):
        """إعداد جدول المرفقات في قاعدة البيانات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # إنشاء جدول المرفقات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS attachments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    entity_type TEXT NOT NULL,
                    entity_id INTEGER NOT NULL,
                    file_name TEXT NOT NULL,
                    original_name TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    file_type TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ تم إعداد جدول المرفقات")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد جدول المرفقات: {e}")

    def load_attachments(self):
        """تحميل قائمة المرفقات"""
        try:
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, file_name, original_name, file_path, file_size, 
                       file_type, description, created_at
                FROM attachments 
                WHERE entity_type = ? AND entity_id = ?
                ORDER BY created_at DESC
            """, (self.entity_type, self.entity_id))
            
            self.attachments_data = cursor.fetchall()
            conn.close()
            
            self.populate_attachments_table()
            self.update_status(f"تم تحميل {len(self.attachments_data)} مرفق")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل المرفقات: {e}")
            self.update_status("خطأ في تحميل المرفقات")

    def populate_attachments_table(self):
        """تعبئة جدول المرفقات"""
        try:
            self.attachments_table.setRowCount(len(self.attachments_data))
            
            for row, attachment in enumerate(self.attachments_data):
                # اسم الملف الأصلي
                name_item = QTableWidgetItem(attachment[2])  # original_name
                name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
                self.attachments_table.setItem(row, 0, name_item)
                
                # نوع الملف
                file_type = attachment[5] or "غير محدد"
                type_item = QTableWidgetItem(file_type)
                type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
                self.attachments_table.setItem(row, 1, type_item)
                
                # حجم الملف
                file_size = self.format_file_size(attachment[4])
                size_item = QTableWidgetItem(file_size)
                size_item.setFlags(size_item.flags() & ~Qt.ItemIsEditable)
                self.attachments_table.setItem(row, 2, size_item)
                
                # تاريخ الإضافة
                created_at = attachment[7][:19] if attachment[7] else "غير محدد"
                date_item = QTableWidgetItem(created_at)
                date_item.setFlags(date_item.flags() & ~Qt.ItemIsEditable)
                self.attachments_table.setItem(row, 3, date_item)
                
                # أزرار الإجراءات
                actions_widget = self.create_actions_widget(attachment[0], attachment[3])
                self.attachments_table.setCellWidget(row, 4, actions_widget)
            
        except Exception as e:
            print(f"❌ خطأ في تعبئة جدول المرفقات: {e}")

    def create_actions_widget(self, attachment_id, file_path):
        """إنشاء أزرار الإجراءات لكل مرفق مع تصميم محسن"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        layout.setAlignment(Qt.AlignCenter)

        # زر فتح/عرض
        open_btn = QPushButton("👁️ فتح")
        open_btn.setToolTip("فتح الملف")
        open_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 6px 10px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 10px;
                min-width: 45px;
                min-height: 30px;
                max-height: 35px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #21618c, stop:1 #1b4f72);
            }
        """)
        open_btn.clicked.connect(lambda: self.open_attachment(file_path))
        layout.addWidget(open_btn)

        # زر تحميل
        download_btn = QPushButton("💾 حفظ")
        download_btn.setToolTip("تحميل الملف")
        download_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 6px 10px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 10px;
                min-width: 45px;
                min-height: 30px;
                max-height: 35px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e8449, stop:1 #196f3d);
            }
        """)
        download_btn.clicked.connect(lambda: self.download_attachment(file_path))
        layout.addWidget(download_btn)

        return widget

    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.setText(message)
        QTimer.singleShot(3000, lambda: self.status_label.setText("جاهز"))

    def add_attachment(self):
        """إضافة مرفق جديد"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختيار ملف للإرفاق",
                "",
                "جميع الملفات (*.*)"
            )
            
            if file_path:
                self.upload_file(file_path)
                
        except Exception as e:
            print(f"❌ خطأ في إضافة المرفق: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة المرفق:\n{str(e)}")

    def upload_file(self, file_path):
        """رفع الملف وحفظه"""
        try:
            # إنشاء مجلد المرفقات
            attachments_dir = Path("data/attachments")
            attachments_dir.mkdir(exist_ok=True)
            
            # إنشاء مجلد فرعي للكيان
            entity_dir = attachments_dir / self.entity_type / str(self.entity_id)
            entity_dir.mkdir(parents=True, exist_ok=True)
            
            # نسخ الملف
            original_name = Path(file_path).name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"{timestamp}_{original_name}"
            destination_path = entity_dir / file_name
            
            shutil.copy2(file_path, destination_path)
            
            # حفظ معلومات الملف في قاعدة البيانات
            file_size = os.path.getsize(file_path)
            file_type = Path(file_path).suffix.lower()
            
            db_path = Path("data/proshipment.db")
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO attachments (entity_type, entity_id, file_name, original_name, 
                                       file_path, file_size, file_type, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (self.entity_type, self.entity_id, file_name, original_name,
                  str(destination_path), file_size, file_type, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            # إعادة تحميل القائمة
            self.load_attachments()
            self.update_status(f"تم إضافة المرفق: {original_name}")
            
        except Exception as e:
            print(f"❌ خطأ في رفع الملف: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء رفع الملف:\n{str(e)}")

    def delete_attachment(self):
        """حذف المرفق المحدد"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        QMessageBox.information(self, "قريباً", "وظيفة الحذف ستكون متوفرة قريباً")

    def open_attachment(self, file_path):
        """فتح المرفق"""
        try:
            import subprocess
            import platform
            
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', file_path])
                
        except Exception as e:
            print(f"❌ خطأ في فتح الملف: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح الملف:\n{str(e)}")

    def download_attachment(self, file_path):
        """تحميل المرفق"""
        try:
            save_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ الملف",
                Path(file_path).name,
                "جميع الملفات (*.*)"
            )
            
            if save_path:
                shutil.copy2(file_path, save_path)
                self.update_status(f"تم تحميل الملف إلى: {save_path}")
                
        except Exception as e:
            print(f"❌ خطأ في تحميل الملف: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الملف:\n{str(e)}")
