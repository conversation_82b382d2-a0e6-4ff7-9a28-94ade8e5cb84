@echo off
chcp 65001 > nul
title نظام إدارة الحوالات - ProShipment Management System

echo.
echo ===============================================
echo    نظام إدارة الحوالات
echo    ProShipment Management System
echo ===============================================
echo.

echo 🚀 جاري تشغيل النظام...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM فحص وجود الملف الرئيسي
if not exist "main.py" (
    echo ❌ الملف الرئيسي main.py غير موجود
    echo تأكد من وجودك في مجلد المشروع الصحيح
    echo.
    pause
    exit /b 1
)

REM تشغيل التطبيق
echo ✅ تشغيل نظام إدارة الحوالات...
python main.py

REM في حالة إغلاق التطبيق
echo.
echo تم إغلاق النظام
pause
