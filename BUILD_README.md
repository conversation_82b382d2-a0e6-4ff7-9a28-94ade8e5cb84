# دليل بناء نظام إدارة الحوالات
# ProShipment Management System Build Guide

## 📋 نظرة عامة

هذا الدليل يشرح كيفية تحويل مشروع نظام إدارة الحوالات إلى ملف تنفيذي (.exe) جاهز للتوزيع على أجهزة Windows.

## 🔧 المتطلبات الأساسية

### متطلبات النظام
- **نظام التشغيل:** Windows 10 أو أحدث
- **Python:** الإصدار 3.8 أو أحدث
- **الذاكرة:** 4 جيجابايت رام كحد أدنى
- **المساحة:** 2 جيجابايت مساحة فارغة

### المتطلبات البرمجية
- Python 3.8+
- pip (أداة تثبيت حزم Python)
- PyInstaller
- PySide6
- جميع متطلبات المشروع (موجودة في requirements.txt)

## 🚀 طرق البناء

### الطريقة الأولى: البناء التلقائي (الأسهل)

1. **تشغيل ملف البناء التلقائي:**
   ```batch
   build_exe.bat
   ```

2. **اتبع التعليمات على الشاشة**

3. **انتظر حتى اكتمال العملية**

### الطريقة الثانية: البناء اليدوي

#### الخطوة 1: تثبيت المتطلبات
```bash
# تشغيل سكريبت تثبيت المتطلبات
python install_requirements.py

# أو تثبيت المتطلبات يدوياً
pip install -r requirements.txt
```

#### الخطوة 2: بناء الملف التنفيذي
```bash
# تشغيل سكريبت البناء
python build.py

# أو استخدام PyInstaller مباشرة
pyinstaller --clean --noconfirm build_config.spec
```

## 📁 هيكل الملفات

```
المشروع/
├── main.py                    # الملف الرئيسي للتطبيق
├── requirements.txt           # قائمة المتطلبات
├── build_config.spec         # تكوين PyInstaller
├── version_info.txt          # معلومات الإصدار
├── build.py                  # سكريبت البناء
├── install_requirements.py   # سكريبت تثبيت المتطلبات
├── build_exe.bat            # ملف البناء التلقائي
├── src/                     # مجلد الكود المصدري
├── release/                 # مجلد الإصدار
├── data/                    # مجلد البيانات
├── assets/                  # مجلد الأصول (أيقونات، صور)
├── build/                   # مجلد البناء المؤقت
└── dist/                    # مجلد الإخراج النهائي
    └── ProShipment/         # مجلد التطبيق الجاهز
        ├── ProShipment.exe  # الملف التنفيذي الرئيسي
        ├── _internal/       # ملفات النظام الداخلية
        ├── data/           # مجلد البيانات
        ├── README.txt      # تعليمات التشغيل
        └── تشغيل_النظام.bat # ملف التشغيل السريع
```

## ⚙️ تخصيص البناء

### تعديل معلومات التطبيق
قم بتعديل ملف `version_info.txt` لتغيير:
- اسم الشركة
- وصف التطبيق
- رقم الإصدار
- حقوق النشر

### إضافة أيقونة مخصصة
1. ضع ملف الأيقونة (.ico) في: `assets/icons/app_icon.ico`
2. سيتم استخدامها تلقائياً في البناء

### تخصيص الملفات المضمنة
قم بتعديل ملف `build_config.spec` لإضافة أو إزالة:
- ملفات البيانات
- مجلدات الموارد
- المكتبات المخفية

## 🧪 اختبار البناء

### اختبار محلي
```bash
# تشغيل التطبيق من المصدر
python main.py

# اختبار الملف التنفيذي
dist/ProShipment/ProShipment.exe
```

### اختبار على أجهزة أخرى
1. انسخ مجلد `dist/ProShipment` كاملاً
2. شغل `ProShipment.exe` على جهاز آخر
3. تأكد من عمل جميع الوظائف

## 📦 التوزيع

### إنشاء حزمة التوزيع
```bash
# ضغط مجلد التطبيق
# يمكن استخدام WinRAR أو 7-Zip
"ProShipment_v1.0.0.zip"
```

### محتويات حزمة التوزيع
- مجلد ProShipment كاملاً
- ملف README.txt مع التعليمات
- ملف تشغيل سريع (.bat)

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ: "Python غير مثبت"
**الحل:**
1. تثبيت Python من [python.org](https://www.python.org/downloads/)
2. تأكد من إضافة Python إلى PATH

#### خطأ: "PyInstaller غير موجود"
**الحل:**
```bash
pip install pyinstaller
```

#### خطأ: "PySide6 غير موجود"
**الحل:**
```bash
pip install PySide6
```

#### خطأ: "فشل في البناء"
**الحل:**
1. تأكد من وجود جميع الملفات المطلوبة
2. فحص رسائل الخطأ في وحدة التحكم
3. تشغيل البناء مع صلاحيات المدير

#### خطأ: "الملف التنفيذي لا يعمل"
**الحل:**
1. تأكد من نسخ مجلد ProShipment كاملاً
2. فحص وجود ملفات _internal
3. تشغيل من وحدة التحكم لرؤية رسائل الخطأ

### سجلات الأخطاء
- فحص ملفات السجل في مجلد `data/logs/`
- تشغيل التطبيق من وحدة التحكم لرؤية الأخطاء

## 📞 الدعم الفني

### للحصول على المساعدة:
1. فحص هذا الدليل أولاً
2. البحث في الأخطاء الشائعة
3. التواصل مع فريق التطوير

### معلومات مفيدة عند طلب المساعدة:
- إصدار Windows
- إصدار Python
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

## 📝 ملاحظات مهمة

### أمان البيانات
- تأكد من نسخ احتياطي لقاعدة البيانات
- لا تشارك ملفات البيانات الحساسة

### الأداء
- الملف التنفيذي أكبر من الكود المصدري
- وقت التشغيل الأول قد يكون أطول
- استخدم SSD لأداء أفضل

### التحديثات
- لتحديث التطبيق، أعد البناء بالكود الجديد
- احتفظ بنسخة من البيانات قبل التحديث

## 🎯 نصائح للتحسين

### تقليل حجم الملف
- إزالة المكتبات غير المستخدمة
- استخدام UPX للضغط
- تحسين الصور والموارد

### تحسين الأداء
- استخدام --onefile للملف الواحد
- تحسين استيراد المكتبات
- تقليل وقت البدء

### سهولة التوزيع
- إنشاء installer باستخدام NSIS
- إضافة تحديث تلقائي
- توقيع رقمي للملف

---

**© 2024 ProShipment Solutions**  
**نظام إدارة الحوالات - دليل البناء والتوزيع**
