{"frozen": {"app.update.auto": false, "app.update.enabled": false, "browser.displayedE10SNotice": 4, "browser.download.manager.showWhenStarting": false, "browser.EULA.override": true, "browser.EULA.3.accepted": true, "browser.link.open_external": 2, "browser.link.open_newwindow": 2, "browser.offline": false, "browser.reader.detectedFirstArticle": true, "browser.safebrowsing.enabled": false, "browser.safebrowsing.malware.enabled": false, "browser.search.update": false, "browser.selfsupport.url": "", "browser.sessionstore.resume_from_crash": false, "browser.shell.checkDefaultBrowser": false, "browser.tabs.warnOnClose": false, "browser.tabs.warnOnOpen": false, "datareporting.healthreport.service.enabled": false, "datareporting.healthreport.uploadEnabled": false, "datareporting.healthreport.service.firstRun": false, "datareporting.healthreport.logging.consoleEnabled": false, "datareporting.policy.dataSubmissionEnabled": false, "datareporting.policy.dataSubmissionPolicyAccepted": false, "devtools.errorconsole.enabled": true, "dom.disable_open_during_load": false, "extensions.autoDisableScopes": 10, "extensions.blocklist.enabled": false, "extensions.checkCompatibility.nightly": false, "extensions.update.enabled": false, "extensions.update.notifyUser": false, "javascript.enabled": true, "network.manage-offline-status": false, "network.http.phishy-userpass-length": 255, "offline-apps.allow_by_default": true, "prompts.tab_modal.enabled": false, "security.fileuri.origin_policy": 3, "security.fileuri.strict_origin_policy": false, "signon.rememberSignons": false, "toolkit.networkmanager.disable": true, "toolkit.telemetry.prompted": 2, "toolkit.telemetry.enabled": false, "toolkit.telemetry.rejected": true, "xpinstall.signatures.required": false, "xpinstall.whitelist.required": false}, "mutable": {"browser.dom.window.dump.enabled": true, "browser.laterrun.enabled": false, "browser.newtab.url": "about:blank", "browser.newtabpage.enabled": false, "browser.startup.page": 0, "browser.startup.homepage": "about:blank", "browser.startup.homepage_override.mstone": "ignore", "browser.usedOnWindows10.introURL": "about:blank", "dom.max_chrome_script_run_time": 30, "dom.max_script_run_time": 30, "dom.report_all_js_exceptions": true, "javascript.options.showInConsole": true, "network.captive-portal-service.enabled": false, "security.csp.enable": false, "startup.homepage_welcome_url": "about:blank", "startup.homepage_welcome_url.additional": "about:blank", "webdriver_accept_untrusted_certs": true, "webdriver_assume_untrusted_issuer": true}}