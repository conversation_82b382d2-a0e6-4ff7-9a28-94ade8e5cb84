@echo off
chcp 65001 > nul
title بناء نظام إدارة الحوالات - ProShipment Build System

echo.
echo ===============================================
echo    بناء نظام إدارة الحوالات
echo    ProShipment Management System Builder
echo ===============================================
echo.

echo 📋 فحص متطلبات البناء...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
python --version

REM فحص وجود pip
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    echo.
    pause
    exit /b 1
)

echo ✅ pip متوفر

echo.
echo 📦 تثبيت/تحديث المتطلبات...
echo.

REM تثبيت المتطلبات
python install_requirements.py
if errorlevel 1 (
    echo.
    echo ❌ فشل في تثبيت المتطلبات
    echo.
    pause
    exit /b 1
)

echo.
echo 🔧 بدء عملية البناء...
echo.

REM بناء الملف التنفيذي
python build.py
if errorlevel 1 (
    echo.
    echo ❌ فشل في بناء الملف التنفيذي
    echo.
    pause
    exit /b 1
)

echo.
echo ===============================================
echo 🎉 تم بناء النظام بنجاح!
echo ===============================================
echo.
echo 📁 مجلد الإخراج: dist\ProShipment\
echo 🚀 الملف التنفيذي: dist\ProShipment\ProShipment.exe
echo.
echo يمكنك الآن توزيع مجلد ProShipment كاملاً
echo أو ضغطه في ملف ZIP للتوزيع
echo.

REM فتح مجلد الإخراج
if exist "dist\ProShipment" (
    echo 📂 فتح مجلد الإخراج...
    explorer "dist\ProShipment"
)

echo.
pause
