#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الحوالات - الملف الرئيسي
ProShipment Management System - Main Entry Point

هذا هو الملف الرئيسي لتشغيل نظام إدارة الحوالات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# استيراد المكتبات المطلوبة
try:
    from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
    from PySide6.QtCore import Qt, QTimer
    from PySide6.QtGui import QPixmap, QFont, QIcon
except ImportError as e:
    print(f"❌ خطأ في استيراد PySide6: {e}")
    print("يرجى تثبيت PySide6 باستخدام: pip install PySide6")
    sys.exit(1)

# استيراد النافذة الرئيسية
try:
    from src.ui.main_window import MainWindow
    print("✅ تم استيراد النافذة الرئيسية من src.ui.main_window")
except ImportError as e:
    print(f"⚠️ فشل استيراد من src.ui.main_window: {e}")
    try:
        from ui.main_window import MainWindow
        print("✅ تم استيراد النافذة الرئيسية من ui.main_window")
    except ImportError as e2:
        print(f"❌ فشل استيراد من ui.main_window: {e2}")
        try:
            # محاولة أخيرة - استيراد مباشر
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
            from ui.main_window import MainWindow
            print("✅ تم استيراد النافذة الرئيسية بالمسار المباشر")
        except ImportError as e3:
            print(f"❌ فشل نهائي في استيراد النافذة الرئيسية: {e3}")
            print("المسارات المتاحة:")
            for path in sys.path:
                print(f"  - {path}")
            sys.exit(1)

def create_data_directory():
    """إنشاء مجلد البيانات إذا لم يكن موجوداً"""
    try:
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)

        # إنشاء مجلدات فرعية
        subdirs = ["attachments", "exports", "backups", "logs"]
        for subdir in subdirs:
            (data_dir / subdir).mkdir(exist_ok=True)

        print("✅ تم إنشاء مجلدات البيانات")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء مجلدات البيانات: {e}")
        return False

def setup_application():
    """إعداد التطبيق الأساسي"""
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)

        # إعداد معلومات التطبيق
        app.setApplicationName("نظام إدارة الحوالات")
        app.setApplicationDisplayName("ProShipment Management System")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("ProShipment Solutions")
        app.setOrganizationDomain("proshipment.com")

        # إعداد الخط الافتراضي
        font = QFont("Segoe UI", 10)
        app.setFont(font)

        # إعداد أيقونة التطبيق (إذا كانت متوفرة)
        icon_path = Path("assets/icons/app_icon.ico")
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))

        print("✅ تم إعداد التطبيق")
        return app
    except Exception as e:
        print(f"❌ خطأ في إعداد التطبيق: {e}")
        return None

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("🚀 بدء تشغيل نظام إدارة الحوالات")
    print("=" * 50)

    try:
        # إنشاء مجلدات البيانات
        if not create_data_directory():
            print("❌ فشل في إنشاء مجلدات البيانات")
            return 1

        # إعداد التطبيق
        app = setup_application()
        if not app:
            print("❌ فشل في إعداد التطبيق")
            return 1

        # إنشاء النافذة الرئيسية
        try:
            main_window = MainWindow()
            main_window.show()

            print("✅ تم تشغيل النظام بنجاح")
            print("🎯 النظام جاهز للاستخدام")

            # تشغيل حلقة الأحداث
            return app.exec()

        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة الرئيسية: {e}")

            # عرض رسالة خطأ للمستخدم
            QMessageBox.critical(
                None,
                "خطأ في التشغيل",
                f"حدث خطأ أثناء تشغيل النظام:\n\n{str(e)}\n\nيرجى التواصل مع الدعم الفني."
            )
            return 1

    except Exception as e:
        print(f"❌ خطأ عام في التطبيق: {e}")
        return 1

if __name__ == "__main__":
    # تشغيل التطبيق
    exit_code = main()
    print(f"\n🔚 انتهى التطبيق برمز الخروج: {exit_code}")
    sys.exit(exit_code)
