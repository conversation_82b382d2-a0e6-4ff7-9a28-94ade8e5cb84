#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بناء نظام إدارة الحوالات
ProShipment Management System Build Script

هذا السكريبت يقوم ببناء ملف تنفيذي (.exe) من المشروع
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f"🔧 {title}")
    print("=" * 60)

def print_step(step, description):
    """طباعة خطوة مع تنسيق"""
    print(f"\n📋 الخطوة {step}: {description}")
    print("-" * 40)

def check_requirements():
    """فحص المتطلبات المطلوبة"""
    print_step(1, "فحص المتطلبات")
    
    # فحص Python
    python_version = sys.version_info
    print(f"✅ إصدار Python: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    # فحص PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller متوفر: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller غير مثبت")
        print("قم بتثبيته باستخدام: pip install pyinstaller")
        return False
    
    # فحص PySide6
    try:
        import PySide6
        print(f"✅ PySide6 متوفر: {PySide6.__version__}")
    except ImportError:
        print("❌ PySide6 غير مثبت")
        print("قم بتثبيته باستخدام: pip install PySide6")
        return False
    
    return True

def clean_build_directories():
    """تنظيف مجلدات البناء السابقة"""
    print_step(2, "تنظيف مجلدات البناء السابقة")
    
    directories_to_clean = ['build', 'dist', '__pycache__']
    
    for directory in directories_to_clean:
        if Path(directory).exists():
            print(f"🗑️ حذف مجلد: {directory}")
            shutil.rmtree(directory)
        else:
            print(f"ℹ️ المجلد غير موجود: {directory}")
    
    print("✅ تم تنظيف مجلدات البناء")

def prepare_build_environment():
    """إعداد بيئة البناء"""
    print_step(3, "إعداد بيئة البناء")
    
    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    data_dir = Path("data")
    if not data_dir.exists():
        data_dir.mkdir()
        print("✅ تم إنشاء مجلد البيانات")
    
    # إنشاء مجلد الأصول إذا لم يكن موجوداً
    assets_dir = Path("assets")
    if not assets_dir.exists():
        assets_dir.mkdir()
        (assets_dir / "icons").mkdir(exist_ok=True)
        print("✅ تم إنشاء مجلد الأصول")
    
    # إنشاء أيقونة افتراضية إذا لم تكن موجودة
    icon_path = assets_dir / "icons" / "app_icon.ico"
    if not icon_path.exists():
        print("ℹ️ لم يتم العثور على أيقونة التطبيق")
        print("سيتم استخدام الأيقونة الافتراضية")
    
    print("✅ تم إعداد بيئة البناء")

def build_executable():
    """بناء الملف التنفيذي"""
    print_step(4, "بناء الملف التنفيذي")
    
    try:
        # تشغيل PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "build_config.spec"
        ]
        
        print(f"🔄 تنفيذ الأمر: {' '.join(cmd)}")
        
        # تشغيل الأمر مع عرض المخرجات
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            encoding='utf-8'
        )
        
        # عرض المخرجات في الوقت الفعلي
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        # فحص نتيجة العملية
        return_code = process.poll()
        
        if return_code == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح")
            return True
        else:
            print(f"❌ فشل في بناء الملف التنفيذي (رمز الخطأ: {return_code})")
            return False
            
    except Exception as e:
        print(f"❌ خطأ أثناء البناء: {e}")
        return False

def verify_build():
    """التحقق من نجاح البناء"""
    print_step(5, "التحقق من نجاح البناء")
    
    dist_dir = Path("dist")
    exe_path = dist_dir / "ProShipment" / "ProShipment.exe"
    
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)  # بالميجابايت
        print(f"✅ تم إنشاء الملف التنفيذي: {exe_path}")
        print(f"📊 حجم الملف: {file_size:.2f} ميجابايت")
        
        # فحص الملفات المرافقة
        required_files = ["ProShipment.exe"]
        missing_files = []
        
        for file_name in required_files:
            file_path = dist_dir / "ProShipment" / file_name
            if file_path.exists():
                print(f"✅ الملف موجود: {file_name}")
            else:
                missing_files.append(file_name)
                print(f"❌ الملف مفقود: {file_name}")
        
        if not missing_files:
            print("✅ جميع الملفات المطلوبة موجودة")
            return True
        else:
            print(f"❌ ملفات مفقودة: {missing_files}")
            return False
    else:
        print(f"❌ لم يتم العثور على الملف التنفيذي: {exe_path}")
        return False

def create_installer_info():
    """إنشاء معلومات التثبيت"""
    print_step(6, "إنشاء معلومات التثبيت")
    
    dist_dir = Path("dist") / "ProShipment"
    
    # إنشاء ملف README
    readme_content = """
نظام إدارة الحوالات
ProShipment Management System
==========================

إصدار: 1.0.0
تاريخ البناء: {build_date}

تعليمات التشغيل:
1. تأكد من وجود جميع الملفات في نفس المجلد
2. قم بتشغيل ProShipment.exe
3. سيتم إنشاء مجلد البيانات تلقائياً عند أول تشغيل

متطلبات النظام:
- Windows 10 أو أحدث
- 4 جيجابايت رام كحد أدنى
- 500 ميجابايت مساحة فارغة

الدعم الفني:
للحصول على الدعم الفني، يرجى التواصل معنا

© 2024 ProShipment Solutions
""".format(build_date=time.strftime("%Y-%m-%d %H:%M:%S"))
    
    readme_path = dist_dir / "README.txt"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ تم إنشاء ملف README: {readme_path}")
    
    # إنشاء ملف batch للتشغيل السريع
    batch_content = """@echo off
chcp 65001 > nul
echo تشغيل نظام إدارة الحوالات...
echo ProShipment Management System
echo.
ProShipment.exe
pause
"""
    
    batch_path = dist_dir / "تشغيل_النظام.bat"
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"✅ تم إنشاء ملف التشغيل السريع: {batch_path}")

def main():
    """الدالة الرئيسية للبناء"""
    print_header("بناء نظام إدارة الحوالات")
    print("ProShipment Management System Build Process")
    
    start_time = time.time()
    
    try:
        # فحص المتطلبات
        if not check_requirements():
            print("\n❌ فشل في فحص المتطلبات")
            return 1
        
        # تنظيف مجلدات البناء
        clean_build_directories()
        
        # إعداد بيئة البناء
        prepare_build_environment()
        
        # بناء الملف التنفيذي
        if not build_executable():
            print("\n❌ فشل في بناء الملف التنفيذي")
            return 1
        
        # التحقق من نجاح البناء
        if not verify_build():
            print("\n❌ فشل في التحقق من البناء")
            return 1
        
        # إنشاء معلومات التثبيت
        create_installer_info()
        
        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        
        print_header("اكتمل البناء بنجاح")
        print(f"⏱️ الوقت المستغرق: {elapsed_time:.2f} ثانية")
        print(f"📁 مجلد الإخراج: dist/ProShipment/")
        print(f"🚀 الملف التنفيذي: dist/ProShipment/ProShipment.exe")
        print("\n🎉 تم بناء النظام بنجاح!")
        print("يمكنك الآن توزيع مجلد ProShipment كاملاً")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف العملية بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n🔚 انتهت عملية البناء برمز الخروج: {exit_code}")
    sys.exit(exit_code)
